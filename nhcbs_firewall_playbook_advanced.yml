---
# nhcbs_firewall_playbook_advanced.yml
# 用于部署和执行 nhcbs 防火墙配置脚本的高级 Ansible Playbook

- name: 部署并执行 NHCBS 防火墙配置
  hosts: "{{ target_hosts | default('all') }}"  # 可通过变量指定目标主机
  become: true  # 使用 sudo 权限执行
  gather_facts: true  # 收集目标主机信息

  vars:
    script_url: "http://fd.ops.public.private.com/nhcbs/nhcbs-firewall.sh"
    temp_script_path: "/tmp/nhcbs-firewall.sh"
    backup_dir: "/tmp/firewall_backup_{{ ansible_date_time.date }}"
    max_retries: 3
    retry_delay: 5
    timeout: 300
    debug_mode: true
    backup_config: true
    verify_after_run: true

  pre_tasks:
    - name: 检查网络连接
      wait_for:
        host: "{{ script_url | urlsplit('hostname') }}"
        port: 80
        timeout: 10
      delegate_to: localhost
      register: network_check
      ignore_errors: yes
      
    - name: 显示网络连接状态
      debug:
        msg: "网络连接状态: {{ 'OK' if network_check is succeeded else '失败' }}"
      
    - name: 中止任务如果网络连接失败
      fail:
        msg: "无法连接到脚本服务器，请检查网络连接"
      when: network_check is failed and not ignore_network_errors | default(false)

  tasks:
    - name: 创建备份目录
      file:
        path: "{{ backup_dir }}"
        state: directory
        mode: '0755'
      when: backup_config | bool
      
    - name: 备份当前防火墙配置
      shell: |
        if command -v firewall-cmd >/dev/null 2>&1; then
          firewall-cmd --list-all > {{ backup_dir }}/firewalld_config.txt
        elif command -v iptables-save >/dev/null 2>&1; then
          iptables-save > {{ backup_dir }}/iptables_config.txt
        fi
      ignore_errors: yes
      when: backup_config | bool
      
    - name: 下载防火墙配置脚本
      get_url:
        url: "{{ script_url }}"
        dest: "{{ temp_script_path }}"
        mode: '0755'
        force: yes
        timeout: 30
      register: download_result
      retries: "{{ max_retries }}"
      delay: "{{ retry_delay }}"
      until: download_result is succeeded
      
    - name: 检查脚本是否成功下载
      stat:
        path: "{{ temp_script_path }}"
      register: script_file
      
    - name: 验证脚本内容
      shell: grep -q "firewall" {{ temp_script_path }}
      register: script_validation
      ignore_errors: yes
      when: script_file.stat.exists
      
    - name: 显示脚本验证结果
      debug:
        msg: "脚本验证: {{ '通过' if script_validation is succeeded else '失败' }}"
      when: script_file.stat.exists
      
    - name: 执行防火墙配置脚本（带调试输出）
      shell: bash -x {{ temp_script_path }}
      register: script_output
      failed_when: script_output.rc != 0
      changed_when: script_output.rc == 0
      async: "{{ timeout }}"
      poll: 5
      when: script_file.stat.exists and (script_validation is succeeded or ignore_validation_errors | default(false))
      
    - name: 显示脚本执行输出
      debug:
        msg: "{{ script_output.stdout_lines }}"
      when: debug_mode | bool and script_output.stdout_lines is defined
      
    - name: 显示脚本执行错误（如果有）
      debug:
        msg: "{{ script_output.stderr_lines }}"
      when: debug_mode | bool and script_output.stderr_lines is defined and script_output.stderr_lines | length > 0
      
    - name: 删除临时脚本文件
      file:
        path: "{{ temp_script_path }}"
        state: absent
      
    - name: 验证防火墙配置
      shell: |
        if command -v firewall-cmd >/dev/null 2>&1; then
          firewall-cmd --state
        elif command -v iptables >/dev/null 2>&1; then
          iptables -L -n | grep -q "policy"
          echo $?
        else
          echo "未找到防火墙服务"
          exit 1
        fi
      register: firewall_verification
      ignore_errors: yes
      when: verify_after_run | bool
      
    - name: 显示防火墙验证结果
      debug:
        msg: "防火墙状态: {{ firewall_verification.stdout if firewall_verification.stdout is defined else '未知' }}"
      when: verify_after_run | bool
      
    - name: 确认防火墙配置完成
      debug:
        msg: "NHCBS 防火墙配置已完成"

  handlers:
    - name: 重启防火墙服务
      service:
        name: "{{ item }}"
        state: restarted
      with_items:
        - firewalld
        - iptables
      ignore_errors: yes
