firewall-cmd --new-zone=vpn --permanent
# 默认拒绝 VPN 用户访问内网
firewall-cmd --zone=public --add-source=**********/16 --permanent
firewall-cmd --zone=public --add-rich-rule='rule family="ipv4" source address="**********/16" destination address="*********/16" drop' --permanent

# 为 user1 添加规则
firewall-cmd --zone=public --add-rich-rule='rule family="ipv4" source address="*************" destination address="*********" accept' --permanent

# 为 user2 添加规则
firewall-cmd --zone=public --add-rich-rule='rule family="ipv4" source address="*************" destination address="**********" accept' --permanent
firewall-cmd --zone=public --add-rich-rule='rule family="ipv4" source address="*************" destination address="**********" accept' --permanent