---
# nhcbs_firewall_playbook.yml
# 用于部署和执行 nhcbs 防火墙配置脚本的 Ansible Playbook

- name: 部署并执行 NHCBS 防火墙配置
  hosts: all  # 可以根据需要修改为特定主机组
  become: true  # 使用 sudo 权限执行
  gather_facts: true  # 收集目标主机信息

  vars:
    script_url: "http://fd.ops.public.private.com/nhcbs/nhcbs-firewall.sh"
    temp_script_path: "/tmp/nhcbs-firewall.sh"

  tasks:
    - name: 确保目标目录存在
      file:
        path: "/tmp"
        state: directory
        mode: '0755'
      
    - name: 下载防火墙配置脚本
      get_url:
        url: "{{ script_url }}"
        dest: "{{ temp_script_path }}"
        mode: '0755'
        force: yes
      register: download_result
      
    - name: 显示下载状态
      debug:
        msg: "脚本下载状态: {{ download_result.status_code | default('未知') }}"
      when: download_result is defined
      
    - name: 执行防火墙配置脚本（带调试输出）
      shell: bash -x {{ temp_script_path }}
      register: script_output
      failed_when: script_output.rc != 0
      changed_when: script_output.rc == 0
      
    - name: 显示脚本执行输出
      debug:
        msg: "{{ script_output.stdout_lines }}"
      when: script_output.stdout_lines is defined
      
    - name: 显示脚本执行错误（如果有）
      debug:
        msg: "{{ script_output.stderr_lines }}"
      when: script_output.stderr_lines is defined and script_output.stderr_lines | length > 0
      
    - name: 删除临时脚本文件
      file:
        path: "{{ temp_script_path }}"
        state: absent
      
    - name: 确认防火墙配置完成
      debug:
        msg: "NHCBS 防火墙配置已完成"

  handlers:
    - name: 重启防火墙服务
      service:
        name: firewalld
        state: restarted
      ignore_errors: yes
