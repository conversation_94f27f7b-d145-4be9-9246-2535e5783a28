---
- name: Execute qh-cedp-key script
  hosts: all
  become: yes
  become_user: root
  become_method: sudo
  tasks:
    - name: Download qh-cedp-key.sh script
      get_url:
        url: http://***********:8888/qh-cedp-key.sh
        dest: /tmp/qh-cedp-key.sh
        mode: '0755'  # Make the script executable

    - name: Execute the script
      shell: bash -x /tmp/qh-cedp-key.sh
      register: script_output

    - name: Display script output
      debug:
        var: script_output.stdout_lines
      when: script_output.stdout_lines is defined

    - name: Remove the script file
      file:
        path: /tmp/qh-cedp-key.sh
        state: absent
