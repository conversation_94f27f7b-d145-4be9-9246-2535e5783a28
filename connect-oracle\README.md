# Oracle 数据库查询工具 (Go 版本)

这是一个用 Go 语言编写的 Oracle 数据库查询工具，编译后可以直接在服务器上运行，无需依赖环境。

## 特性

- 🚀 编译后的二进制文件，无需运行时依赖
- 📝 YAML 配置文件，易于管理
- 🔍 支持多种查询模式
- 📊 详细的日志记录
- 💾 结果可导出为 JSON 格式
- 🛠️ 跨平台支持 (Linux, Windows, macOS)

## 文件说明

- `main.go`: 主程序文件
- `config.go`: 配置管理模块
- `database.go`: 数据库操作模块
- `logger.go`: 日志记录模块
- `config.yaml`: 配置文件
- `go.mod`: Go 模块文件
- `Makefile`: 构建脚本
- `README.md`: 使用说明文档

## 快速开始

### 1. 编译程序

```bash
# 下载依赖并编译
make build

# 或者手动编译
go mod download
go build -o oracle-query .
```

### 2. 生成配置文件

```bash
# 生成默认配置文件
./oracle-query -gen-config
```

### 3. 修改配置

编辑 `config.yaml` 文件，设置您的数据库连接信息：

```yaml
database:
  host: "********"
  port: 1521
  service_name: "ORCL"
  username: "admin"
  password: "pass"
```

### 4. 运行程序

```bash
# 执行主查询
./oracle-query

# 测试数据库连接
./oracle-query -query=test

# 检查表是否存在
./oracle-query -query=check
```

## 详细使用说明

### 命令行参数

```bash
./oracle-query [选项]
```

**选项:**
- `-config string`: 配置文件路径 (默认: "config.yaml")
- `-query string`: 查询类型 (main|test|check|info) (默认: "main")
- `-output string`: 输出文件路径 (JSON格式)
- `-verbose`: 详细输出
- `-version`: 显示版本信息
- `-gen-config`: 生成默认配置文件
- `-help`: 显示帮助信息

### 查询类型

1. **main** - 执行主查询 (默认)
   ```bash
   ./oracle-query -query=main
   ```

2. **test** - 测试数据库连接
   ```bash
   ./oracle-query -query=test
   ```

3. **check** - 检查必需的表是否存在
   ```bash
   ./oracle-query -query=check
   ```

4. **info** - 获取表结构信息
   ```bash
   ./oracle-query -query=info
   ```

### 使用示例

```bash
# 执行主查询并保存结果
./oracle-query -query=main -output=result.json

# 使用自定义配置文件
./oracle-query -config=/path/to/custom-config.yaml

# 详细输出模式
./oracle-query -verbose

# 测试连接
./oracle-query -query=test -verbose
```

## 配置文件说明

`config.yaml` 文件包含以下配置项：

### 数据库配置
```yaml
database:
  host: "********"        # 数据库主机地址
  port: 1521               # 数据库端口
  service_name: "ORCL"     # 服务名
  username: "admin"        # 用户名
  password: "pass"         # 密码
```

### 连接配置
```yaml
connection:
  timeout: 30              # 连接超时时间（秒）
  max_idle_conns: 2        # 最大空闲连接数
  max_open_conns: 10       # 最大打开连接数
  conn_max_lifetime: 300   # 连接最大生存时间（秒）
```

### 查询配置
```yaml
query:
  timeout: 60              # 查询超时时间（秒）
```

### 日志配置
```yaml
logging:
  level: "info"            # 日志级别 (debug|info|warn|error)
  file: "oracle_query.log" # 日志文件路径
  console: true            # 是否输出到控制台
```

### SQL 配置
```yaml
sql:
  main_query: |            # 主查询 SQL
    SELECT COUNT(a.id) as total_count
    FROM bas_instation_task a
    INNER JOIN bas_instation_task_detail b ON a.msg_id = b.msg_id
    LEFT JOIN bas_user c ON b.send_user_id = c.user_id
    LEFT JOIN (
        SELECT * FROM BAS_CONFIG_value WHERE key_id = 49
    ) d ON d.value_code = b.bus_type
    WHERE a.task_Status IN (10, 20)
    AND a.receive_user_id = 509548
    AND (b.app_show = 1 OR b.app_show IS NULL)
```

## 构建和部署

### 本地构建

```bash
# 构建当前平台
make build

# 构建所有平台
make build-all

# 清理构建文件
make clean
```

### 跨平台编译

```bash
# Linux
GOOS=linux GOARCH=amd64 go build -o oracle-query-linux .

# Windows
GOOS=windows GOARCH=amd64 go build -o oracle-query-windows.exe .

# macOS
GOOS=darwin GOARCH=amd64 go build -o oracle-query-macos .
```

### 服务器部署

1. 将编译好的二进制文件上传到服务器
2. 上传配置文件 `config.yaml`
3. 设置执行权限：`chmod +x oracle-query`
4. 运行程序：`./oracle-query`

## 故障排除

### 1. 编译失败

确保安装了 Go 1.19 或更高版本，并且启用了 CGO：
```bash
go version
export CGO_ENABLED=1
```

### 2. 连接失败

- 检查数据库服务器是否可达
- 验证端口是否正确
- 确认用户名和密码
- 检查服务名是否正确
- 确保 Oracle 客户端库已安装

### 3. 表不存在错误

- 确认表名大小写是否正确
- 检查用户是否有访问这些表的权限
- 验证表是否在正确的模式（schema）中

### 4. 权限问题

确保程序有权限：
- 读取配置文件
- 写入日志文件
- 写入输出文件（如果指定）

## 日志文件

程序运行时会生成日志文件（默认为 `oracle_query.log`），包含：
- 连接信息
- 查询执行时间
- 错误信息
- 调试信息（在详细模式下）

## 安全注意事项

1. 保护配置文件，避免密码泄露
2. 使用最小权限的数据库用户
3. 定期轮换数据库密码
4. 确保日志文件的访问权限
5. 在生产环境中禁用调试日志
