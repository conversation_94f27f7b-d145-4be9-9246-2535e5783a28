# Oracle 数据库查询脚本

这个目录包含用于连接 Oracle 数据库并执行查询的 Python 脚本。

## 文件说明

- `oracle_query.py`: 基础版本的 Oracle 查询脚本
- `oracle_query_advanced.py`: 高级版本，支持命令行参数和多种查询模式
- `config.py`: 数据库配置文件
- `requirements.txt`: Python 依赖包列表
- `README.md`: 使用说明文档

## 安装依赖

### 1. 安装 Oracle Instant Client

首先需要安装 Oracle Instant Client：

**Windows:**
1. 下载 Oracle Instant Client Basic 包
2. 解压到某个目录（如 C:\oracle\instantclient_21_8）
3. 将该目录添加到 PATH 环境变量

**Linux:**
```bash
# CentOS/RHEL
sudo yum install oracle-instantclient-basic

# Ubuntu/Debian
sudo apt-get install oracle-instantclient-basic
```

### 2. 安装 Python 依赖

```bash
pip install -r requirements.txt
```

或者单独安装：

```bash
pip install cx_Oracle
```

## 数据库配置

编辑 `config.py` 文件中的数据库连接参数：

```python
DATABASE_CONFIG = {
    'host': '********',
    'port': 1521,
    'service_name': 'ORCL',  # 根据实际情况调整
    'username': 'admin',
    'password': 'pass'
}
```

## 使用方法

### 基础版本

```bash
python oracle_query.py
```

### 高级版本

#### 执行主查询（默认）

```bash
python oracle_query_advanced.py
```

#### 测试数据库连接

```bash
python oracle_query_advanced.py -q test
```

#### 检查表是否存在

```bash
python oracle_query_advanced.py -q check
```

#### 使用自定义数据库参数

```bash
python oracle_query_advanced.py --host ******** --port 1521 --service ORCL --user admin --password pass
```

#### 详细输出模式

```bash
python oracle_query_advanced.py -v
```

#### 保存结果到文件

```bash
python oracle_query_advanced.py -o results.json
```

## 查询说明

脚本执行的主要查询是：

```sql
SELECT COUNT(a.id) as total_count
FROM bas_instation_task a
INNER JOIN bas_instation_task_detail b ON a.msg_id = b.msg_id
LEFT JOIN bas_user c ON b.send_user_id = c.user_id
LEFT JOIN (
    SELECT * FROM BAS_CONFIG_value WHERE key_id = 49
) d ON d.value_code = b.bus_type
WHERE a.task_Status IN (10, 20)
AND a.receive_user_id = 509548
AND (b.app_show = 1 OR b.app_show IS NULL)
```

这个查询统计符合特定条件的任务记录数量。

## 故障排除

### 1. cx_Oracle 安装失败

确保已正确安装 Oracle Instant Client，并且 PATH 环境变量包含 Instant Client 目录。

### 2. 连接失败

- 检查数据库服务器是否可达
- 验证端口是否正确
- 确认用户名和密码
- 检查服务名是否正确

### 3. 表不存在错误

- 确认表名大小写是否正确
- 检查用户是否有访问这些表的权限
- 验证表是否在正确的模式（schema）中

### 4. 编码问题

如果遇到中文字符显示问题，可以在 `config.py` 中调整编码设置：

```python
QUERY_CONFIG = {
    'encoding': 'UTF-8'  # 或 'GBK'
}
```

## 日志文件

脚本运行时会生成 `oracle_query.log` 日志文件，包含详细的执行信息和错误信息。

## 安全注意事项

1. 不要在代码中硬编码密码
2. 考虑使用环境变量或配置文件来存储敏感信息
3. 确保日志文件不包含敏感信息
4. 在生产环境中使用适当的数据库用户权限
