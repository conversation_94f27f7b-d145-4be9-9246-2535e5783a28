#!/bin/bash

# Oracle Query Tool 构建脚本

set -e

APP_NAME="oracle-query"
VERSION="1.0.0"

echo "Oracle Query Tool 构建脚本 v${VERSION}"
echo "=================================="

# 检查 Go 是否安装
if ! command -v go &> /dev/null; then
    echo "错误: 未找到 Go 编译器，请先安装 Go"
    exit 1
fi

echo "Go 版本: $(go version)"

# 检查 CGO 是否启用
if [ "${CGO_ENABLED:-1}" != "1" ]; then
    echo "警告: CGO 未启用，正在启用 CGO..."
    export CGO_ENABLED=1
fi

# 创建构建目录
BUILD_DIR="build"
mkdir -p ${BUILD_DIR}

# 下载依赖
echo "正在下载依赖..."
go mod download
go mod tidy

# 构建应用程序
echo "正在构建应用程序..."

# 检测操作系统
OS=$(uname -s | tr '[:upper:]' '[:lower:]')
ARCH=$(uname -m)

case ${ARCH} in
    x86_64)
        ARCH="amd64"
        ;;
    aarch64|arm64)
        ARCH="arm64"
        ;;
    *)
        echo "警告: 未知的架构 ${ARCH}，使用 amd64"
        ARCH="amd64"
        ;;
esac

BINARY_NAME="${APP_NAME}"
if [ "${OS}" = "windows" ] || [ "${OS}" = "mingw"* ] || [ "${OS}" = "cygwin"* ]; then
    BINARY_NAME="${APP_NAME}.exe"
fi

echo "目标平台: ${OS}/${ARCH}"
echo "二进制文件: ${BUILD_DIR}/${BINARY_NAME}"

# 构建
LDFLAGS="-ldflags \"-X main.Version=${VERSION} -X main.AppName='Oracle Query Tool'\""

eval "CGO_ENABLED=1 GOOS=${OS} GOARCH=${ARCH} go build ${LDFLAGS} -o ${BUILD_DIR}/${BINARY_NAME} ."

if [ $? -eq 0 ]; then
    echo "构建成功!"
    echo "二进制文件位置: ${BUILD_DIR}/${BINARY_NAME}"
    
    # 复制配置文件
    if [ -f "config.yaml" ]; then
        cp config.yaml ${BUILD_DIR}/
        echo "配置文件已复制到构建目录"
    fi
    
    # 显示文件信息
    echo ""
    echo "构建结果:"
    ls -la ${BUILD_DIR}/
    
    echo ""
    echo "使用方法:"
    echo "  cd ${BUILD_DIR}"
    echo "  ./${BINARY_NAME} -help"
    
else
    echo "构建失败!"
    exit 1
fi
