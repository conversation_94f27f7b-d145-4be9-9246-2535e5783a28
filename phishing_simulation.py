# -*- coding: utf-8 -*-
import requests
from urllib3.exceptions import InsecureRequestWarning

def simulate_phishing_request():
    """
    模拟钓鱼攻击请求以测试Suricata规则引擎
    规则ID: sid:2032372
    仅用于内部测试环境验证规则检测能力
    """
    # 禁用不安全HTTPS警告
    requests.packages.urllib3.disable_warnings(InsecureRequestWarning)
    
    # 根据规则构造特定的JavaScript混淆代码
    obfuscated_js = (
        # 第一部分：eval(unescape('...'))
        "eval(unescape(" + "'%66%75%6e%63%74%69%6f%6e" +  # function
        "%20%6d%61%69%6e%28%29" +  # main()
        "%7b%0a%09%76%61%72%20" +  # {\n\tvar 
        "%78%3b" +  # x;
        "%0a%09%76%61%72%20%74%6d%70%20%3d%20%73%2e%73%70%6c%69%74%28%22" +  # \n\tvar tmp = s.split("
        "%41%42%43%44%45%46%47%48%49" +  # ABCDEFGHI (8个hex字符)
        "%22%29%3b" +  # ");
        "%0a%09%78%20%3d%20%75%6e%65%73%63%61%70%65%28%74%6d%70%5b%30%5d%29%3b" +  # \n\tx = unescape(tmp[0]);
        "%0a%09%6b%20%3d%20%75%6e%65%73%63%61%70%65%28%74%6d%70%5b%31%5d%20%2b%20%22" +  # \n\tk = unescape(tmp[1] + "
        "%41%42%43%44%45%46" +  # ABCDEF
        "%22%29%3b%0a%09" +  # ");\n\t
        "%53%74%72%69%6e%67%2e%66%72%6f%6d%43%68%61%72%43%6f%64%65%28%28%70%61%72%73%65%49%6e%74%28" +  # String.fromCharCode((parseInt(
        "%31%30" +  # 10
        "%29%29%29" +  # )))
        "')); " +
        # 第二部分：eval(unescape('document.write'))
        "eval(unescape('%64%6f%63%75%6d%65%6e%74%2e%77%72%69%74%65')); " +
        # 第三部分：unescape('')
        "unescape('%27%29%29%3b');"
    )
    
    # 构造HTML响应，确保内容长度小于8192字节
    html_content = f'''
    <html>
    <head><title>Login Page</title></head>
    <body>
        <form id="login-form">
            <script>
            {obfuscated_js}
            </script>
        </form>
    </body>
    </html>
    '''
    
    try:
        # 发送请求到指定的URL
        response = requests.post(
            "http://nacos.ops.private.com/nacos",
            headers={
                'Content-Type': 'text/html',
                'User-Agent': 'Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1)',
                'Accept': 'text/html,application/xhtml+xml',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate'
            },
            data=html_content.encode('utf-8'),
            verify=False
        )
        
        print("\n=== Suricata规则测试分析 ===")
        print(f"请求URL: http://nacos.ops.private.com/nacos")
        print(f"状态码: {response.status_code}")
        print(f"Content-Type: {response.headers.get('Content-Type', 'text/html')}")
        print(f"响应大小: {len(html_content)} 字节")
        
        print("\nSuricata规则(sid:2032372)匹配特征:")
        print("1. HTTP响应状态码: 200")
        print("2. Content-Type: text/html")
        print("3. 内容长度 < 8192字节")
        print("4. JavaScript混淆特征:")
        print("   - eval(unescape())模式")
        print("   - hex编码的function关键字")
        print("   - var tmp = s.split()模式")
        print("   - 8个hex字符的分隔符")
        print("   - unescape(tmp[])操作")
        print("   - String.fromCharCode(parseInt())调用")
        print("   - document.write混淆")
        print("\n注意：此脚本仅用于内部测试环境验证规则检测能力")
        
    except requests.exceptions.RequestException as e:
        print(f"\n=== 请求失败: {str(e)} ===")

if __name__ == "__main__":
    simulate_phishing_request()