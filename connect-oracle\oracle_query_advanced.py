#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Oracle 数据库查询脚本 - 高级版本
支持配置文件、多种查询模式和错误处理
"""

import cx_Oracle
import logging
import sys
import os
import argparse
import json
from datetime import datetime
from config import DATABASE_CONFIG, QUERY_CONFIG, LOG_CONFIG, SQL_QUERIES

# 配置日志
def setup_logging(level=None):
    """设置日志配置"""
    log_level = getattr(logging, (level or LOG_CONFIG['level']).upper())
    logging.basicConfig(
        level=log_level,
        format=LOG_CONFIG['format'],
        handlers=[
            logging.FileHandler(LOG_CONFIG['file']),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

logger = setup_logging()

class OracleDatabase:
    """Oracle 数据库操作类"""
    
    def __init__(self, config=None):
        """
        初始化数据库连接
        
        Args:
            config: 数据库配置字典
        """
        self.config = config or DATABASE_CONFIG
        self.connection = None
        self.cursor = None
        
    def __enter__(self):
        """上下文管理器入口"""
        self.connect()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()
        
    def connect(self):
        """连接到 Oracle 数据库"""
        try:
            # 构建 DSN
            dsn = cx_Oracle.makedsn(
                self.config['host'], 
                self.config['port'], 
                service_name=self.config['service_name']
            )
            
            logger.info(f"连接到 Oracle: {self.config['host']}:{self.config['port']}/{self.config['service_name']}")
            
            # 建立连接
            self.connection = cx_Oracle.connect(
                user=self.config['username'],
                password=self.config['password'],
                dsn=dsn,
                encoding=QUERY_CONFIG.get('encoding', 'UTF-8')
            )
            
            # 设置会话参数
            self.cursor = self.connection.cursor()
            if QUERY_CONFIG.get('fetch_size'):
                self.cursor.arraysize = QUERY_CONFIG['fetch_size']
                
            logger.info("数据库连接成功")
            return True
            
        except cx_Oracle.Error as e:
            error_obj, = e.args
            logger.error(f"Oracle 错误 {error_obj.code}: {error_obj.message}")
            return False
        except Exception as e:
            logger.error(f"连接失败: {e}")
            return False
    
    def test_connection(self):
        """测试数据库连接"""
        try:
            result = self.execute_query(SQL_QUERIES['test_connection'])
            if result:
                logger.info("数据库连接测试成功")
                return True
            return False
        except Exception as e:
            logger.error(f"连接测试失败: {e}")
            return False
    
    def check_tables(self):
        """检查所需表是否存在"""
        try:
            result = self.execute_query(SQL_QUERIES['check_tables'])
            if result:
                tables = [row[0] for row in result]
                logger.info(f"找到表: {', '.join(tables)}")
                
                required_tables = ['BAS_INSTATION_TASK', 'BAS_INSTATION_TASK_DETAIL', 'BAS_USER', 'BAS_CONFIG_VALUE']
                missing_tables = [table for table in required_tables if table not in tables]
                
                if missing_tables:
                    logger.warning(f"缺少表: {', '.join(missing_tables)}")
                    return False
                else:
                    logger.info("所有必需的表都存在")
                    return True
            return False
        except Exception as e:
            logger.error(f"检查表时出错: {e}")
            return False
    
    def execute_query(self, sql, params=None):
        """
        执行查询语句
        
        Args:
            sql: SQL 语句
            params: 查询参数
            
        Returns:
            查询结果
        """
        try:
            if not self.cursor:
                raise Exception("数据库未连接")
            
            logger.debug(f"执行 SQL: {sql}")
            if params:
                logger.debug(f"参数: {params}")
            
            start_time = datetime.now()
            
            if params:
                self.cursor.execute(sql, params)
            else:
                self.cursor.execute(sql)
            
            results = self.cursor.fetchall()
            end_time = datetime.now()
            
            execution_time = (end_time - start_time).total_seconds()
            logger.info(f"查询完成，耗时: {execution_time:.3f} 秒，返回 {len(results)} 行")
            
            return results
            
        except cx_Oracle.Error as e:
            error_obj, = e.args
            logger.error(f"SQL 执行错误 {error_obj.code}: {error_obj.message}")
            return None
        except Exception as e:
            logger.error(f"查询执行失败: {e}")
            return None
    
    def get_column_info(self):
        """获取查询结果的列信息"""
        if self.cursor and self.cursor.description:
            return [(desc[0], desc[1].__name__) for desc in self.cursor.description]
        return []
    
    def close(self):
        """关闭数据库连接"""
        try:
            if self.cursor:
                self.cursor.close()
                logger.debug("游标已关闭")
            
            if self.connection:
                self.connection.close()
                logger.info("数据库连接已关闭")
                
        except Exception as e:
            logger.error(f"关闭连接时出错: {e}")

def format_results(results, columns):
    """格式化查询结果"""
    if not results:
        return "查询结果为空"
    
    output = []
    output.append("查询结果:")
    output.append("-" * 50)
    
    # 显示列名
    if columns:
        col_names = [col[0] for col in columns]
        output.append(f"列: {', '.join(col_names)}")
        output.append("-" * 50)
    
    # 显示数据
    for i, row in enumerate(results, 1):
        if len(row) == 1:
            output.append(f"第 {i} 行: {row[0]}")
        else:
            output.append(f"第 {i} 行: {', '.join(str(val) for val in row)}")
    
    return "\n".join(output)

def save_results_to_file(results, columns, filename):
    """保存结果到文件"""
    try:
        data = {
            'timestamp': datetime.now().isoformat(),
            'columns': columns,
            'results': [list(row) for row in results],
            'row_count': len(results)
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2, default=str)
        
        logger.info(f"结果已保存到: {filename}")
        return True
    except Exception as e:
        logger.error(f"保存结果失败: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Oracle 数据库查询工具')
    parser.add_argument('-q', '--query', choices=['main', 'test', 'check'], 
                       default='main', help='选择要执行的查询')
    parser.add_argument('-o', '--output', help='输出文件路径')
    parser.add_argument('-v', '--verbose', action='store_true', help='详细输出')
    parser.add_argument('--host', help='数据库主机地址')
    parser.add_argument('--port', type=int, help='数据库端口')
    parser.add_argument('--service', help='服务名')
    parser.add_argument('--user', help='用户名')
    parser.add_argument('--password', help='密码')
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logger.setLevel(logging.DEBUG)
    
    # 更新配置
    config = DATABASE_CONFIG.copy()
    if args.host:
        config['host'] = args.host
    if args.port:
        config['port'] = args.port
    if args.service:
        config['service_name'] = args.service
    if args.user:
        config['username'] = args.user
    if args.password:
        config['password'] = args.password
    
    # 选择查询
    query_map = {
        'main': SQL_QUERIES['main_query'],
        'test': SQL_QUERIES['test_connection'],
        'check': SQL_QUERIES['check_tables']
    }
    
    sql = query_map.get(args.query)
    if not sql:
        logger.error(f"未知的查询类型: {args.query}")
        sys.exit(1)
    
    try:
        # 使用上下文管理器
        with OracleDatabase(config) as db:
            # 测试连接
            if not db.test_connection():
                logger.error("数据库连接测试失败")
                sys.exit(1)
            
            # 如果是主查询，先检查表
            if args.query == 'main':
                if not db.check_tables():
                    logger.warning("表检查失败，但继续执行查询")
            
            # 执行查询
            results = db.execute_query(sql)
            
            if results is not None:
                columns = db.get_column_info()
                
                # 显示结果
                output = format_results(results, columns)
                print(output)
                
                # 保存到文件
                if args.output:
                    save_results_to_file(results, columns, args.output)
                
                # 特殊处理主查询结果
                if args.query == 'main' and results:
                    count = results[0][0] if results[0] else 0
                    logger.info(f"符合条件的记录总数: {count}")
                    
            else:
                logger.error("查询执行失败")
                sys.exit(1)
                
    except KeyboardInterrupt:
        logger.info("用户中断程序")
    except Exception as e:
        logger.error(f"程序执行错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
