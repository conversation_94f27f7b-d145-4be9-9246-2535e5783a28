package main

import (
	"encoding/base64"
	"fmt"
)

// urlSafeBase64Encode 将输入的字符串进行URL安全的Base64编码
func urlSafeBase64Encode(text string) string {
	return base64.URLEncoding.EncodeToString([]byte(text))
}

func main() {
	fontText := "相寓"
	encodeText := urlSafeBase64Encode(fontText)
	fmt.Printf("text = %s, base64 encodeText = %s\n", fontText, encodeText)
	a := base64.URLEncoding.EncodeToString([]byte("相寓"))
	fmt.Println("aaaa ", a)
}
