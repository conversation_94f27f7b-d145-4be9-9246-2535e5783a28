package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"os"
	"path/filepath"
)

// 版本信息
const (
	Version = "1.0.0"
	AppName = "Oracle Query Tool"
)

// 命令行参数
var (
	configPath   = flag.String("config", "config.yaml", "配置文件路径")
	queryType    = flag.String("query", "main", "查询类型 (main|test|check|info)")
	outputFile   = flag.String("output", "", "输出文件路径 (JSON格式)")
	verbose      = flag.Bool("verbose", false, "详细输出")
	version      = flag.Bool("version", false, "显示版本信息")
	genConfig    = flag.Bool("gen-config", false, "生成默认配置文件")
	showHelp     = flag.Bool("help", false, "显示帮助信息")
)

func main() {
	flag.Parse()

	// 显示版本信息
	if *version {
		fmt.Printf("%s v%s\n", AppName, Version)
		os.Exit(0)
	}

	// 显示帮助信息
	if *showHelp {
		showUsage()
		os.Exit(0)
	}

	// 生成默认配置文件
	if *genConfig {
		if err := generateDefaultConfig(); err != nil {
			fmt.Fprintf(os.Stderr, "生成配置文件失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Printf("默认配置文件已生成: %s\n", *configPath)
		os.Exit(0)
	}

	// 加载配置
	config, err := LoadConfig(*configPath)
	if err != nil {
		fmt.Fprintf(os.Stderr, "加载配置失败: %v\n", err)
		fmt.Fprintf(os.Stderr, "提示: 使用 -gen-config 生成默认配置文件\n")
		os.Exit(1)
	}

	// 如果启用详细输出，设置日志级别为 DEBUG
	if *verbose {
		config.Logging.Level = "debug"
	}

	// 创建日志记录器
	logger, err := NewLogger(config.Logging)
	if err != nil {
		fmt.Fprintf(os.Stderr, "创建日志记录器失败: %v\n", err)
		os.Exit(1)
	}
	defer logger.Close()

	logger.Infof("启动 %s v%s", AppName, Version)
	logger.Debugf("使用配置文件: %s", *configPath)

	// 创建数据库连接
	db, err := NewDatabase(config, logger)
	if err != nil {
		logger.Errorf("创建数据库连接失败: %v", err)
		os.Exit(1)
	}
	defer db.Close()

	// 测试数据库连接
	if err := db.Connect(); err != nil {
		logger.Errorf("数据库连接失败: %v", err)
		os.Exit(1)
	}

	// 根据查询类型执行相应操作
	var result *QueryResult
	switch *queryType {
	case "test":
		if err := db.ExecuteTestQuery(); err != nil {
			logger.Errorf("测试查询失败: %v", err)
			os.Exit(1)
		}
		logger.Info("测试查询成功")
		return

	case "check":
		if err := db.CheckTables(); err != nil {
			logger.Errorf("表检查失败: %v", err)
			os.Exit(1)
		}
		logger.Info("表检查完成")
		return

	case "info":
		result, err = db.GetTableInfo()
		if err != nil {
			logger.Errorf("获取表信息失败: %v", err)
			os.Exit(1)
		}

	case "main":
		// 先检查表（可选）
		if err := db.CheckTables(); err != nil {
			logger.Warnf("表检查失败，但继续执行主查询: %v", err)
		}

		result, err = db.ExecuteMainQuery()
		if err != nil {
			logger.Errorf("主查询失败: %v", err)
			os.Exit(1)
		}

	default:
		logger.Errorf("未知的查询类型: %s", *queryType)
		showUsage()
		os.Exit(1)
	}

	// 显示结果
	if result != nil {
		displayResult(result, logger)

		// 保存到文件
		if *outputFile != "" {
			if err := saveResultToFile(result, *outputFile, logger); err != nil {
				logger.Errorf("保存结果失败: %v", err)
				os.Exit(1)
			}
		}
	}

	logger.Info("程序执行完成")
}

// showUsage 显示使用说明
func showUsage() {
	fmt.Printf("%s v%s - Oracle 数据库查询工具\n\n", AppName, Version)
	fmt.Println("用法:")
	fmt.Printf("  %s [选项]\n\n", filepath.Base(os.Args[0]))
	fmt.Println("选项:")
	flag.PrintDefaults()
	fmt.Println("\n查询类型:")
	fmt.Println("  main  - 执行主查询 (默认)")
	fmt.Println("  test  - 测试数据库连接")
	fmt.Println("  check - 检查必需的表是否存在")
	fmt.Println("  info  - 获取表结构信息")
	fmt.Println("\n示例:")
	fmt.Printf("  %s                              # 执行主查询\n", filepath.Base(os.Args[0]))
	fmt.Printf("  %s -query=test                  # 测试连接\n", filepath.Base(os.Args[0]))
	fmt.Printf("  %s -query=main -output=result.json  # 执行主查询并保存结果\n", filepath.Base(os.Args[0]))
	fmt.Printf("  %s -verbose                     # 详细输出\n", filepath.Base(os.Args[0]))
	fmt.Printf("  %s -gen-config                  # 生成默认配置文件\n", filepath.Base(os.Args[0]))
}

// generateDefaultConfig 生成默认配置文件
func generateDefaultConfig() error {
	return SaveDefaultConfig(*configPath)
}

// displayResult 显示查询结果
func displayResult(result *QueryResult, logger *Logger) {
	if result.Count == 0 {
		logger.Info("查询结果为空")
		return
	}

	logger.Infof("查询结果 (%d 行):", result.Count)
	logger.Info("列名: " + fmt.Sprintf("%v", result.Columns))

	// 显示前几行数据
	maxRows := 10
	if result.Count < maxRows {
		maxRows = result.Count
	}

	for i := 0; i < maxRows; i++ {
		logger.Infof("第 %d 行: %v", i+1, result.Rows[i])
	}

	if result.Count > maxRows {
		logger.Infof("... 还有 %d 行数据", result.Count-maxRows)
	}
}

// saveResultToFile 保存结果到文件
func saveResultToFile(result *QueryResult, filename string, logger *Logger) error {
	// 确保目录存在
	dir := filepath.Dir(filename)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建目录失败: %v", err)
	}

	// 创建输出数据
	output := map[string]interface{}{
		"timestamp": fmt.Sprintf("%v", os.Getenv("timestamp")),
		"query_type": *queryType,
		"result": result,
	}

	// 序列化为 JSON
	data, err := json.MarshalIndent(output, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化结果失败: %v", err)
	}

	// 写入文件
	if err := os.WriteFile(filename, data, 0644); err != nil {
		return fmt.Errorf("写入文件失败: %v", err)
	}

	logger.Infof("结果已保存到: %s", filename)
	return nil
}
