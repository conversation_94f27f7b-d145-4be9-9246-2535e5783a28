# Oracle 数据库配置
database:
  host: "********"
  port: 1521
  service_name: "ORCL"  # 根据实际情况调整，可能是 XE, XEPDB1 等
  username: "admin"
  password: "pass"
  
# 连接配置
connection:
  timeout: 30  # 连接超时时间（秒）
  max_idle_conns: 2
  max_open_conns: 10
  conn_max_lifetime: 300  # 连接最大生存时间（秒）

# 查询配置
query:
  timeout: 60  # 查询超时时间（秒）
  
# 日志配置
logging:
  level: "info"  # debug, info, warn, error
  file: "oracle_query.log"
  console: true

# SQL 查询语句
sql:
  # 主查询 - 统计符合条件的记录数
  main_query: |
    SELECT COUNT(a.id) as total_count
    FROM bas_instation_task a
    INNER JOIN bas_instation_task_detail b ON a.msg_id = b.msg_id
    LEFT JOIN bas_user c ON b.send_user_id = c.user_id
    LEFT JOIN (
        SELECT * FROM BAS_CONFIG_value WHERE key_id = 49
    ) d ON d.value_code = b.bus_type
    WHERE a.task_Status IN (10, 20)
    AND a.receive_user_id = 509548
    AND (b.app_show = 1 OR b.app_show IS NULL)
  
  # 测试连接查询
  test_connection: "SELECT 1 FROM DUAL"
  
  # 检查表是否存在
  check_tables: |
    SELECT table_name 
    FROM user_tables 
    WHERE table_name IN ('BAS_INSTATION_TASK', 'BAS_INSTATION_TASK_DETAIL', 'BAS_USER', 'BAS_CONFIG_VALUE')
  
  # 获取表结构信息
  table_info: |
    SELECT table_name, column_name, data_type, nullable
    FROM user_tab_columns 
    WHERE table_name IN ('BAS_INSTATION_TASK', 'BAS_INSTATION_TASK_DETAIL', 'BAS_USER', 'BAS_CONFIG_VALUE')
    ORDER BY table_name, column_id
