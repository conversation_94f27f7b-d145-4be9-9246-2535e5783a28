#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库配置文件
"""

# Oracle 数据库连接配置
DATABASE_CONFIG = {
    'host': '********',
    'port': 1521,
    'service_name': 'ORCL',  # 可能需要根据实际情况调整为 XE, XEPDB1 等
    'username': 'admin',
    'password': 'pass'
}

# 可选的其他配置
QUERY_CONFIG = {
    'timeout': 30,  # 查询超时时间（秒）
    'fetch_size': 1000,  # 每次获取的记录数
    'encoding': 'UTF-8'
}

# 日志配置
LOG_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(levelname)s - %(message)s',
    'file': 'oracle_query.log'
}

# SQL 查询语句
SQL_QUERIES = {
    'main_query': """
        SELECT COUNT(a.id) as total_count
        FROM bas_instation_task a
        INNER JOIN bas_instation_task_detail b ON a.msg_id = b.msg_id
        LEFT JOIN bas_user c ON b.send_user_id = c.user_id
        LEFT JOIN (
            SELECT * FROM BAS_CONFIG_value WHERE key_id = 49
        ) d ON d.value_code = b.bus_type
        WHERE a.task_Status IN (10, 20)
        AND a.receive_user_id = 509548
        AND (b.app_show = 1 OR b.app_show IS NULL)
    """,
    
    'test_connection': "SELECT 1 FROM DUAL",
    
    'check_tables': """
        SELECT table_name 
        FROM user_tables 
        WHERE table_name IN ('BAS_INSTATION_TASK', 'BAS_INSTATION_TASK_DETAIL', 'BAS_USER', 'BAS_CONFIG_VALUE')
    """
}
