---
- name: Update Categraf Service
  hosts: all
  become: yes
  become_user: super
  become_method: sudo
  become_user: root
  gather_facts: True
  tasks:
    - name: Update Categraf to v0.4.3
      shell:
        cmd: ./categraf --update --update_url http://***********:5000/package/categraf/categraf-v0.4.3-linux-amd64.tar.gz
        chdir: /usr/local/categraf

    - name: Download new config.toml
      get_url:
        url: http://***********:5000/package/categraf/config-qh.toml
        dest: /tmp/config.toml
        mode: '0644'

    - name: Backup existing config.toml
      copy:
        src: /usr/local/categraf/conf/config.toml
        dest: /usr/local/categraf/conf/config.toml-{{ ansible_date_time.date }}
        remote_src: yes

    - name: Move new config.toml to destination
      copy:
        src: /tmp/config.toml
        dest: /usr/local/categraf/conf/config.toml
        remote_src: yes

    - name: Download new ntp.toml
      get_url:
        url: http://***********:5000/package/categraf/ntp-qh.toml
        dest: /tmp/ntp.toml
        mode: '0644'

    - name: Backup existing ntp.toml
      copy:
        src: /usr/local/categraf/conf/input.ntp/ntp.toml
        dest: /usr/local/categraf/conf/input.ntp/ntp.toml-{{ ansible_date_time.date }}
        remote_src: yes

    - name: Move new ntp.toml to destination
      copy:
        src: /tmp/ntp.toml
        dest: /usr/local/categraf/conf/input.ntp/ntp.toml
        remote_src: yes

    - name: Update categraf.service StandardOutput
      replace:
        path: /etc/systemd/system/categraf.service
        regexp: 'StandardOutput=journal\+console'
        replace: 'StandardOutput=journal'
        backup: yes

    - name: Update categraf.service StandardError
      replace:
        path: /etc/systemd/system/categraf.service
        regexp: 'StandardError=journal\+console'
        replace: 'StandardError=journal'

    - name: Reload systemd daemon
      systemd:
        daemon_reload: yes

    - name: Restart categraf service
      systemd:
        name: categraf
        state: restarted
