# NHCBS 防火墙配置 Ansible Playbook

这个仓库包含用于部署和执行 NHCBS 防火墙配置脚本的 Ansible Playbook。

## 文件说明

- `nhcbs_firewall_playbook.yml`: 基础版本的 Ansible Playbook，实现基本功能
- `nhcbs_firewall_playbook_advanced.yml`: 高级版本，包含更多错误处理和可选功能

## 基本用法

### 1. 执行基础版本

```bash
ansible-playbook nhcbs_firewall_playbook.yml
```

这将在所有目标主机上执行防火墙配置脚本。

### 2. 执行高级版本

```bash
ansible-playbook nhcbs_firewall_playbook_advanced.yml
```

### 3. 指定目标主机

```bash
ansible-playbook nhcbs_firewall_playbook.yml -e "target_hosts=webservers"
```

### 4. 使用自定义变量

```bash
ansible-playbook nhcbs_firewall_playbook_advanced.yml -e "debug_mode=false backup_config=true"
```

## 高级配置选项

高级版本支持以下配置选项：

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| target_hosts | all | 目标主机组 |
| script_url | http://fd.ops.public.private.com/nhcbs/nhcbs-firewall.sh | 脚本URL |
| temp_script_path | /tmp/nhcbs-firewall.sh | 临时脚本路径 |
| backup_dir | /tmp/firewall_backup_日期 | 备份目录 |
| max_retries | 3 | 最大重试次数 |
| retry_delay | 5 | 重试延迟（秒） |
| timeout | 300 | 脚本执行超时时间（秒） |
| debug_mode | true | 是否显示详细调试信息 |
| backup_config | true | 是否备份当前防火墙配置 |
| verify_after_run | true | 是否验证防火墙配置 |
| ignore_network_errors | false | 是否忽略网络连接错误 |
| ignore_validation_errors | false | 是否忽略脚本验证错误 |

## 示例：在特定环境中执行

### 开发环境

```bash
ansible-playbook nhcbs_firewall_playbook_advanced.yml -e "target_hosts=dev_servers"
```

### 生产环境

```bash
ansible-playbook nhcbs_firewall_playbook_advanced.yml -e "target_hosts=prod_servers debug_mode=false verify_after_run=true"
```

## 注意事项

1. 确保目标主机可以访问脚本URL
2. 确保目标主机上有足够的权限执行脚本
3. 建议在生产环境执行前先在测试环境验证
4. 如果脚本执行失败，可以查看日志了解详细信息

## 故障排除

### 脚本下载失败

检查网络连接和URL是否正确：

```bash
ansible-playbook nhcbs_firewall_playbook_advanced.yml -e "max_retries=5 retry_delay=10"
```

### 脚本执行超时

增加超时时间：

```bash
ansible-playbook nhcbs_firewall_playbook_advanced.yml -e "timeout=600"
```

### 防火墙验证失败

检查脚本是否正确配置了防火墙：

```bash
ansible-playbook nhcbs_firewall_playbook_advanced.yml -e "debug_mode=true"
```
