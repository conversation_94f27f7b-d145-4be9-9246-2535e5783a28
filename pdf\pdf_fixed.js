"use strict";
var page = require('webpage').create(),
    system = require('system');

// 辅助函数：从 URL 中提取域名
function extractDomain(url) {
    var domain;
    try {
        if (url.indexOf("://") > -1) {
            domain = url.split('/')[2];
        } else {
            domain = url.split('/')[0];
        }
        // 移除端口号
        domain = domain.split(':')[0];
        return domain;
    } catch (e) {
        console.log('Error extracting domain from URL: ' + url);
        return 'localhost';
    }
}

if (system.args.length < 2) {
    console.log('Usage: createPDF.js URL filename [cookie]');
    phantom.exit(1);
} else {
    var address = system.args[1];
    var output = system.args[2];
    var cookie = system.args[3];
    
    console.log('address = ' , address);
    console.log('output = ' , output);
    console.log('cookie = ' , cookie);
    
    // 设置页面大小
    page.paperSize = {
        format: 'A4',
        margin: "1cm",
        header: {
            height: "1cm",
            contents: phantom.callback(function (pageNum, numPages) {
                return "";
            })
        },
        footer: {
            height: "1cm",
            contents: phantom.callback(function (pageNum, numPages) {
                return "";
            })
        }
    };
    
    // 设置 User-Agent
    page.settings.userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
    
    // Cookie 处理 - 关键修复
    if (cookie) {
        console.log('Processing cookie: ' + cookie);
        
        // 方法1: 直接设置 Cookie 头
        page.customHeaders = {
            'Cookie': cookie
        };
        
        // 方法2: 使用 phantom.addCookie (在打开页面前设置)
        var domain = extractDomain(address);
        console.log('Extracted domain: ' + domain);
        
        // 解析 cookie 字符串
        var cookieParts = cookie.split(';');
        for (var i = 0; i < cookieParts.length; i++) {
            var cookiePart = cookieParts[i].trim();
            if (cookiePart) {
                var equalIndex = cookiePart.indexOf('=');
                if (equalIndex > 0) {
                    var cookieName = cookiePart.substring(0, equalIndex).trim();
                    var cookieValue = cookiePart.substring(equalIndex + 1).trim();
                    
                    console.log('Setting cookie: ' + cookieName + ' = ' + cookieValue + ' for domain: ' + domain);
                    
                    // 添加 cookie
                    var success = phantom.addCookie({
                        'name': cookieName,
                        'value': cookieValue,
                        'domain': domain,
                        'path': '/',
                        'httponly': false,
                        'secure': false
                    });
                    
                    console.log('Cookie add result: ' + success);
                }
            }
        }
        
        // 验证 cookie 是否设置成功
        var cookies = phantom.cookies;
        console.log('Current cookies count: ' + cookies.length);
        for (var j = 0; j < cookies.length; j++) {
            console.log('Cookie ' + j + ': ' + cookies[j].name + ' = ' + cookies[j].value + ' (domain: ' + cookies[j].domain + ')');
        }
    }
    
    // 添加请求拦截器来调试
    page.onResourceRequested = function(requestData, networkRequest) {
        console.log('Request: ' + requestData.url);
        if (requestData.headers) {
            for (var k = 0; k < requestData.headers.length; k++) {
                if (requestData.headers[k].name.toLowerCase() === 'cookie') {
                    console.log('Cookie header: ' + requestData.headers[k].value);
                }
            }
        }
    };
    
    page.onResourceReceived = function(response) {
        if (response.stage === 'start') {
            console.log('Response: ' + response.url + ' (status: ' + response.status + ')');
            if (response.status >= 400) {
                console.log('HTTP Error: ' + response.status + ' - ' + response.statusText);
            }
        }
    };
    
    page.onError = function(msg, trace) {
        console.log('Page error: ' + msg);
    };
    
    // 打开页面
    page.open(address, function (status) {
        console.log('Page open status: ' + status);
        
        if (status !== 'success') {
            console.log('Failed to load page: ' + address);
            phantom.exit(1);
        } else {
            console.log('Page loaded successfully');
            
            // 检查页面是否需要异步渲染
            var isAsync = page.evaluate(function () {
                return window.async_printing;
            });
            
            if (isAsync) {
                console.log('Waiting for async rendering...');
                waitFor(function testAsyncIsDone() {
                    return page.evaluate(function () {
                        return window.async_printing_done;
                    });
                }, render);
            } else {
                console.log('Rendering immediately...');
                render();
            }
        }
    });
}

var timer;

function render() {
    console.log('Starting render process...');
    window.setTimeout(function () {
        console.log('Rendering PDF to: ' + output);
        page.render(output);
        console.log('PDF generation completed');
        clearTimeout(timer);
        phantom.exit();
    }, 1500);
}

// 设置超时
timer = setTimeout(function () {
    console.log('Timeout reached, exiting...');
    phantom.exit(1);
}, 60 * 1000 * 5);

function waitFor(test, callback) {
    if (test()) {
        callback();
    } else {
        setTimeout(function () {
            waitFor(test, callback);
        }, 50);
    }
}
