# 弱口令密码扫描工具

这是一个用于扫描网站弱口令密码的Python工具。该工具可以尝试使用常见的弱密码登录指定的网站，并报告成功的尝试。

## 功能特点

- 支持自定义密码字典
- 多线程并发扫描
- 自动检测和处理CSRF令牌
- 详细的日志记录
- 结果保存为JSON格式

## 安装依赖

```bash
pip install requests beautifulsoup4
```

## 使用方法

基本用法：

```bash
python weak_password_scanner.py -u http://jupyter-lab-test.5i5j.com/login?next=%2Flab%3F
```

使用自定义密码字典：

```bash
python weak_password_scanner.py -u http://jupyter-lab-test.5i5j.com/login?next=%2Flab%3F -f passwords.txt
```

指定用户名（如果已知）：

```bash
python weak_password_scanner.py -u http://jupyter-lab-test.5i5j.com/login?next=%2Flab%3F -n admin
```

调整线程数和超时时间：

```bash
python weak_password_scanner.py -u http://jupyter-lab-test.5i5j.com/login?next=%2Flab%3F -t 10 --timeout 15
```

自定义输出文件：

```bash
python weak_password_scanner.py -u http://jupyter-lab-test.5i5j.com/login?next=%2Flab%3F -o results.json
```

启用调试模式：

```bash
python weak_password_scanner.py -u http://jupyter-lab-test.5i5j.com/login?next=%2Flab%3F -d
```

添加请求延迟（防止被封锁）：

```bash
python weak_password_scanner.py -u http://jupyter-lab-test.5i5j.com/login?next=%2Flab%3F -s 1.5
```

详细模式（显示更多信息但不如调试模式详细）：

```bash
python weak_password_scanner.py -u http://jupyter-lab-test.5i5j.com/login?next=%2Flab%3F -v
```

测试单个密码：

```bash
python weak_password_scanner.py -u http://jupyter-lab-test.5i5j.com/login?next=%2Flab%3F -p "admin123"
```

指定用户名和密码：

```bash
python weak_password_scanner.py -u http://jupyter-lab-test.5i5j.com/login?next=%2Flab%3F -n admin -p "admin123"
```

## 参数说明

- `-u, --url`: 目标URL（必需）
- `-n, --username`: 用户名（如果已知）
- `-f, --password-file`: 密码字典文件路径
- `-p, --password`: 指定单个密码进行测试（不使用密码字典）
- `-t, --threads`: 线程数（默认: 5）
- `-o, --output`: 输出文件路径（默认: scan_results.json）
- `--timeout`: 请求超时时间（默认: 10秒）
- `-d, --debug`: 启用调试模式，显示详细的调试信息
- `-v, --verbose`: 显示详细信息（不如调试模式详细）
- `-s, --sleep`: 每次请求之间的延迟时间（秒）（默认: 0）
- `--user-field`: 用户名字段名（默认: username）
- `--pass-field`: 密码字段名（默认: password）

## 注意事项

1. 请确保您有合法权限测试目标系统
2. 过于频繁的请求可能会被目标系统封锁
3. 本工具仅用于安全测试和教育目的

## 输出示例

```json
{
    "target": "http://jupyter-lab-test.5i5j.com/login?next=%2Flab%3F",
    "scan_time": "2023-06-01 12:34:56",
    "total_passwords_tested": 100,
    "successful_passwords": [
        {
            "username": "admin",
            "password": "admin123"
        },
        {
            "username": "admin",
            "password": "password123"
        }
    ]
}
```
