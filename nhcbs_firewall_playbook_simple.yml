---
# nhcbs_firewall_playbook_simple.yml
# 最简化版本，完全对应原始命令

- name: 执行 NHCBS 防火墙配置脚本
  hosts: all
  become: true
  gather_facts: false

  tasks:
    - name: 下载并执行防火墙脚本，然后删除
      shell: wget http://fd.ops.public.private.com/nhcbs/nhcbs-firewall.sh -O /tmp/nhcbs-firewall.sh && bash -x /tmp/nhcbs-firewall.sh && rm -f /tmp/nhcbs-firewall.sh
      args:
        executable: /bin/bash
      register: script_result
      
    - name: 显示执行结果
      debug:
        var: script_result.stdout_lines
      when: script_result.stdout_lines is defined
