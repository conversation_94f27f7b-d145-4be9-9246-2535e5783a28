"use strict";
var page = require('webpage').create(),
    system = require('system');

// 辅助函数：从 URL 中提取域名
function extractDomain(url) {
    var domain;
    try {
        if (url.indexOf("://") > -1) {
            domain = url.split('/')[2];
        } else {
            domain = url.split('/')[0];
        }
        // 移除端口号
        domain = domain.split(':')[0];
        return domain;
    } catch (e) {
        console.log('Error extracting domain from URL: ' + url);
        return 'localhost';
    }
}

if (system.args.length < 2) {
    console.log('Usage: createPDF.js URL filename');
    phantom.exit(1);
} else {
    var address = system.args[1];
    var output = system.args[2];
    var cookie = system.args[3];
    console.log('address = ' , address);
    console.log('output = ' , output);
    console.log('cookie = ' , cookie);
    // page.viewportSize = {width: 1000, height: 1500};
    page.paperSize = {
        format: 'A4',
        margin: "1cm",
        /* default header/footer for pages that don't have custom overwrites (see below) */
        header: {
            height: "1cm",
            contents: phantom.callback(function (pageNum, numPages) {
                return "";
            })
        },
        footer: {
            height: "1cm",
            contents: phantom.callback(function (pageNum, numPages) {
                return "";
            })
        }
    };
    if (cookie) {
        console.log('Setting cookie: ' + cookie);

        // 方法1: 使用 customHeaders 设置 Cookie（注意大小写）
        page.customHeaders = {
            'Cookie': cookie
        };

        // 方法2: 使用 phantom.addCookie 方法
        // 解析 cookie 字符串 (支持 name=value 格式)
        try {
            var cookieParts = cookie.split(';');
            for (var i = 0; i < cookieParts.length; i++) {
                var cookiePart = cookieParts[i].trim();
                if (cookiePart) {
                    var equalIndex = cookiePart.indexOf('=');
                    if (equalIndex > 0) {
                        var cookieName = cookiePart.substring(0, equalIndex).trim();
                        var cookieValue = cookiePart.substring(equalIndex + 1).trim();

                        console.log('Adding cookie: ' + cookieName + ' = ' + cookieValue);

                        // 添加 cookie 到 phantom
                        phantom.addCookie({
                            'name': cookieName,
                            'value': cookieValue,
                            'domain': extractDomain(address),
                            'path': '/',
                            'httponly': false,
                            'secure': false
                        });
                    }
                }
            }
        } catch (e) {
            console.log('Cookie parsing error: ' + e.message);
        }
    }
    // 添加错误处理
    page.onError = function(msg, trace) {
        console.log('Page error: ' + msg);
        if (trace && trace.length) {
            console.log('Trace:');
            trace.forEach(function(t) {
                console.log(' -> ' + (t.file || t.sourceURL) + ': ' + t.line + (t.function ? ' (in function ' + t.function + ')' : ''));
            });
        }
    };

    page.onResourceError = function(resourceError) {
        console.log('Resource error (' + resourceError.url + '): ' + resourceError.errorString);
    };

    page.onResourceReceived = function(response) {
        if (response.stage === 'end') {
            console.log('Resource received: ' + response.url + ' (status: ' + response.status + ')');
        }
    };

    page.open(address, function (status) {
        console.log('Page open status: ' + status);
        if (status !== 'success') {
            console.log('Unable to load the address: ' + address);
            console.log('Please check:');
            console.log('1. URL is accessible');
            console.log('2. Cookie format is correct');
            console.log('3. Server accepts the cookie');
            phantom.exit(1);
        } else {
            console.log('Page loaded successfully');
            /* 如何在页面中自定义页眉和页脚？
               使用下面的方式可以设置

               样例:
               <html>
                 <head>
                   <script type="text/javascript">
                     var PhantomJSPrinting = {
                        header: {
                            height: "1cm",
                            contents: function(pageNum, numPages) {
                                return pageNum + "/" + numPages;
                            }
                        },
                        footer: {
                            height: "1cm",
                            contents: function(pageNum, numPages) {
                                return pageNum + "/" + numPages;
                            }
                        }
                     };
                   </script>
                 </head>
                 <body>
                    <h1>asdfadsf</h1><p>asdfadsfycvx</p>
                 </body>
              </html>
            */
            var PhantomJSPrinting = page.evaluate(function () {
                return window.PhantomJSPrinting;
            });
            if (PhantomJSPrinting) {
                var paperSize = page.paperSize;

                if (PhantomJSPrinting.orientation) {
                    paperSize.orientation = PhantomJSPrinting.orientation;
                }

                if (PhantomJSPrinting.format) {
                    paperSize.format = PhantomJSPrinting.format;
                }
                if (PhantomJSPrinting.width) {
                    paperSize.width = PhantomJSPrinting.width;
                }
                if (PhantomJSPrinting.height) {
                    paperSize.height = PhantomJSPrinting.height;
                }

                if (PhantomJSPrinting.margin) {
                    paperSize.margin = PhantomJSPrinting.margin;
                }

                if (PhantomJSPrinting.header) {
                    paperSize.header.height = PhantomJSPrinting.header.height;

                    paperSize.header.contents = phantom.callback(function (pageNum, numPages) {
                        return page.evaluate(function (pageNum, numPages) {
                            return PhantomJSPrinting.header.contents(pageNum, numPages);
                        }, pageNum, numPages);
                    });
                } else if (PhantomJSPrinting.header === false || PhantomJSPrinting.header === null) {
                    paperSize.header = null;
                }

                if (PhantomJSPrinting.footer) {
                    paperSize.footer.height = PhantomJSPrinting.footer.height;
                    paperSize.footer.contents = phantom.callback(function (pageNum, numPages) {
                        return page.evaluate(function (pageNum, numPages) {
                            return PhantomJSPrinting.footer.contents(pageNum, numPages);
                        }, pageNum, numPages);
                    });
                } else if (PhantomJSPrinting.footer === false || PhantomJSPrinting.footer === null) {
                    paperSize.footer = null;
                }
                page.paperSize = paperSize;
            }

            /*
             异步初始化的程序？
             需要声明您是异步渲染的程序，我们将会等待您的渲染结束，像这样
             <html>
                 <head>
                    <script src="jquery.js"></script>
                   <script type="text/javascript">
                     window.async_printing = true;// 需要全局声明您的程序是异步渲染的

                     $(function(){
                        $.get('path/to/action' , function(data){
                            $('#userName').text(data);

                            // 声明 async_printing_done 来表示您的程序已经渲染完成了
                            window.async_printing_done = true;
                        })
                     })
                   </script>
                 </head>
                 <body>
                    <h1 id="userName"></h1><p>asdfadsfycvx</p>
                 </body>
              </html>
             */

            if (
                page.evaluate(function () {
                    return window.async_printing
                })
            ) {
                // 是异步加载的页面
                waitFor(function testAsyncIsDone() {
                    return page.evaluate(function () {
                        return window.async_printing_done
                    })
                }, render);
            } else {
                render();
            }
        }
    });
}

var timer;

function render() {
    window.setTimeout(function () {
        page.render(output);
        clearTimeout(timer);
        phantom.exit();
    }, 1500);
}

timer = setTimeout(function () {
    phantom.exit();
}, 60 * 1000*5);

function waitFor(test, callback) {
    if (test()) {
        callback();
    } else {
        setTimeout(function () {
            waitFor(test, callback);
        }, 50);
    }
}