"use strict";
var page = require('webpage').create(),
    system = require('system');

if (system.args.length < 2) {
    console.log('Usage: createPDF.js URL filename');
    phantom.exit(1);
} else {
    var address = system.args[1];
    var output = system.args[2];
    var cookie = system.args[3];
    console.log('address = ' , address);
    console.log('output = ' , output);
    console.log('cookie = ' , cookie);
    // page.viewportSize = {width: 1000, height: 1500};
    page.paperSize = {
        format: 'A4',
        margin: "1cm",
        /* default header/footer for pages that don't have custom overwrites (see below) */
        header: {
            height: "1cm",
            contents: phantom.callback(function (pageNum, numPages) {
                return "";
            })
        },
        footer: {
            height: "1cm",
            contents: phantom.callback(function (pageNum, numPages) {
                return "";
            })
        }
    };
    if (cookie) {
        page.customHeaders = {'cookie': cookie};
    }
    page.open(address, function (status) {
        if (status !== 'success') {
            console.log('Unable to load the address!');
        } else {
            /* 如何在页面中自定义页眉和页脚？
               使用下面的方式可以设置

               样例:
               <html>
                 <head>
                   <script type="text/javascript">
                     var PhantomJSPrinting = {
                        header: {
                            height: "1cm",
                            contents: function(pageNum, numPages) {
                                return pageNum + "/" + numPages;
                            }
                        },
                        footer: {
                            height: "1cm",
                            contents: function(pageNum, numPages) {
                                return pageNum + "/" + numPages;
                            }
                        }
                     };
                   </script>
                 </head>
                 <body>
                    <h1>asdfadsf</h1><p>asdfadsfycvx</p>
                 </body>
              </html>
            */
            var PhantomJSPrinting = page.evaluate(function () {
                return window.PhantomJSPrinting;
            });
            if (PhantomJSPrinting) {
                var paperSize = page.paperSize;

                if (PhantomJSPrinting.orientation) {
                    paperSize.orientation = PhantomJSPrinting.orientation;
                }

                if (PhantomJSPrinting.format) {
                    paperSize.format = PhantomJSPrinting.format;
                }
                if (PhantomJSPrinting.width) {
                    paperSize.width = PhantomJSPrinting.width;
                }
                if (PhantomJSPrinting.height) {
                    paperSize.height = PhantomJSPrinting.height;
                }

                if (PhantomJSPrinting.margin) {
                    paperSize.margin = PhantomJSPrinting.margin;
                }

                if (PhantomJSPrinting.header) {
                    paperSize.header.height = PhantomJSPrinting.header.height;

                    paperSize.header.contents = phantom.callback(function (pageNum, numPages) {
                        return page.evaluate(function (pageNum, numPages) {
                            return PhantomJSPrinting.header.contents(pageNum, numPages);
                        }, pageNum, numPages);
                    });
                } else if (PhantomJSPrinting.header === false || PhantomJSPrinting.header === null) {
                    paperSize.header = null;
                }

                if (PhantomJSPrinting.footer) {
                    paperSize.footer.height = PhantomJSPrinting.footer.height;
                    paperSize.footer.contents = phantom.callback(function (pageNum, numPages) {
                        return page.evaluate(function (pageNum, numPages) {
                            return PhantomJSPrinting.footer.contents(pageNum, numPages);
                        }, pageNum, numPages);
                    });
                } else if (PhantomJSPrinting.footer === false || PhantomJSPrinting.footer === null) {
                    paperSize.footer = null;
                }
                page.paperSize = paperSize;
            }

            /*
             异步初始化的程序？
             需要声明您是异步渲染的程序，我们将会等待您的渲染结束，像这样
             <html>
                 <head>
                    <script src="jquery.js"></script>
                   <script type="text/javascript">
                     window.async_printing = true;// 需要全局声明您的程序是异步渲染的

                     $(function(){
                        $.get('path/to/action' , function(data){
                            $('#userName').text(data);

                            // 声明 async_printing_done 来表示您的程序已经渲染完成了
                            window.async_printing_done = true;
                        })
                     })
                   </script>
                 </head>
                 <body>
                    <h1 id="userName"></h1><p>asdfadsfycvx</p>
                 </body>
              </html>
             */

            if (
                page.evaluate(function () {
                    return window.async_printing
                })
            ) {
                // 是异步加载的页面
                waitFor(function testAsyncIsDone() {
                    return page.evaluate(function () {
                        return window.async_printing_done
                    })
                }, render);
            } else {
                render();
            }
        }
    });
}

var timer;

function render() {
    window.setTimeout(function () {
        page.render(output);
        clearTimeout(timer);
        phantom.exit();
    }, 1500);
}

timer = setTimeout(function () {
    phantom.exit();
}, 60 * 1000*5);

function waitFor(test, callback) {
    if (test()) {
        callback();
    } else {
        setTimeout(function () {
            waitFor(test, callback);
        }, 50);
    }
}