package main

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	_ "github.com/godror/godror"
)

// Database 数据库操作结构体
type Database struct {
	db     *sql.DB
	config *Config
	logger *Logger
}

// QueryResult 查询结果
type QueryResult struct {
	Columns []string        `json:"columns"`
	Rows    [][]interface{} `json:"rows"`
	Count   int             `json:"count"`
}

// NewDatabase 创建新的数据库连接
func NewDatabase(config *Config, logger *Logger) (*Database, error) {
	dsn := config.Database.GetDSN()
	logger.Debugf("连接字符串: %s", maskPassword(dsn))

	// 打开数据库连接
	db, err := sql.Open("godror", dsn)
	if err != nil {
		return nil, fmt.Errorf("打开数据库连接失败: %v", err)
	}

	// 设置连接池参数
	db.SetMaxIdleConns(config.Connection.MaxIdleConns)
	db.SetMaxOpenConns(config.Connection.MaxOpenConns)
	db.SetConnMaxLifetime(time.Duration(config.Connection.ConnMaxLifetime) * time.Second)

	database := &Database{
		db:     db,
		config: config,
		logger: logger,
	}

	return database, nil
}

// maskPassword 隐藏密码
func maskPassword(dsn string) string {
	// 简单的密码隐藏，实际使用中可以更复杂
	return "***masked***"
}

// Connect 测试数据库连接
func (d *Database) Connect() error {
	ctx, cancel := context.WithTimeout(context.Background(), 
		time.Duration(d.config.Connection.Timeout)*time.Second)
	defer cancel()

	d.logger.Info("正在测试数据库连接...")
	
	if err := d.db.PingContext(ctx); err != nil {
		return fmt.Errorf("数据库连接测试失败: %v", err)
	}

	d.logger.Info("数据库连接成功")
	return nil
}

// ExecuteQuery 执行查询
func (d *Database) ExecuteQuery(sql string) (*QueryResult, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 
		time.Duration(d.config.Query.Timeout)*time.Second)
	defer cancel()

	d.logger.Debugf("执行查询: %s", sql)
	
	start := time.Now()
	rows, err := d.db.QueryContext(ctx, sql)
	if err != nil {
		return nil, fmt.Errorf("查询执行失败: %v", err)
	}
	defer rows.Close()

	// 获取列信息
	columns, err := rows.Columns()
	if err != nil {
		return nil, fmt.Errorf("获取列信息失败: %v", err)
	}

	// 读取数据
	var results [][]interface{}
	for rows.Next() {
		// 创建接收数据的切片
		values := make([]interface{}, len(columns))
		valuePtrs := make([]interface{}, len(columns))
		for i := range values {
			valuePtrs[i] = &values[i]
		}

		// 扫描行数据
		if err := rows.Scan(valuePtrs...); err != nil {
			return nil, fmt.Errorf("扫描行数据失败: %v", err)
		}

		// 处理 NULL 值和类型转换
		row := make([]interface{}, len(columns))
		for i, val := range values {
			if val == nil {
				row[i] = nil
			} else {
				switch v := val.(type) {
				case []byte:
					row[i] = string(v)
				default:
					row[i] = v
				}
			}
		}
		results = append(results, row)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("读取结果集失败: %v", err)
	}

	duration := time.Since(start)
	d.logger.Infof("查询完成，耗时: %.3f 秒，返回 %d 行", duration.Seconds(), len(results))

	return &QueryResult{
		Columns: columns,
		Rows:    results,
		Count:   len(results),
	}, nil
}

// ExecuteTestQuery 执行测试查询
func (d *Database) ExecuteTestQuery() error {
	d.logger.Info("执行连接测试查询...")
	
	result, err := d.ExecuteQuery(d.config.SQL.TestConnection)
	if err != nil {
		return err
	}

	if result.Count > 0 {
		d.logger.Info("连接测试查询成功")
		return nil
	}

	return fmt.Errorf("连接测试查询返回空结果")
}

// CheckTables 检查表是否存在
func (d *Database) CheckTables() error {
	d.logger.Info("检查必需的表是否存在...")
	
	result, err := d.ExecuteQuery(d.config.SQL.CheckTables)
	if err != nil {
		return err
	}

	if result.Count == 0 {
		return fmt.Errorf("未找到任何必需的表")
	}

	d.logger.Infof("找到 %d 个表:", result.Count)
	for _, row := range result.Rows {
		if len(row) > 0 {
			d.logger.Infof("  - %v", row[0])
		}
	}

	// 检查是否所有必需的表都存在
	requiredTables := []string{"BAS_INSTATION_TASK", "BAS_INSTATION_TASK_DETAIL", "BAS_USER", "BAS_CONFIG_VALUE"}
	foundTables := make(map[string]bool)
	
	for _, row := range result.Rows {
		if len(row) > 0 {
			if tableName, ok := row[0].(string); ok {
				foundTables[tableName] = true
			}
		}
	}

	var missingTables []string
	for _, table := range requiredTables {
		if !foundTables[table] {
			missingTables = append(missingTables, table)
		}
	}

	if len(missingTables) > 0 {
		d.logger.Warnf("缺少以下表: %v", missingTables)
		return fmt.Errorf("缺少必需的表: %v", missingTables)
	}

	d.logger.Info("所有必需的表都存在")
	return nil
}

// ExecuteMainQuery 执行主查询
func (d *Database) ExecuteMainQuery() (*QueryResult, error) {
	d.logger.Info("执行主查询...")
	
	result, err := d.ExecuteQuery(d.config.SQL.MainQuery)
	if err != nil {
		return nil, err
	}

	// 如果是计数查询，显示结果
	if result.Count > 0 && len(result.Rows[0]) > 0 {
		count := result.Rows[0][0]
		d.logger.Infof("查询结果: 符合条件的记录总数为 %v", count)
	}

	return result, nil
}

// GetTableInfo 获取表结构信息
func (d *Database) GetTableInfo() (*QueryResult, error) {
	d.logger.Info("获取表结构信息...")
	
	return d.ExecuteQuery(d.config.SQL.TableInfo)
}

// Close 关闭数据库连接
func (d *Database) Close() error {
	if d.db != nil {
		d.logger.Info("关闭数据库连接")
		return d.db.Close()
	}
	return nil
}
