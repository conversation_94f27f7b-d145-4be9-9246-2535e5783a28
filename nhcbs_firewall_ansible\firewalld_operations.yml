---
# firewalld_operations.yml
# 用于管理 firewalld 防火墙的常见操作的 Ansible Playbook

- name: 管理 Firewalld 防火墙配置
  hosts: "{{ target_hosts | default('all') }}"
  become: true
  gather_facts: true

  vars:
    firewalld_zone: "{{ firewalld_zone | default('public') }}"
    permanent: "{{ permanent | default(true) }}"
    backup_dir: "/tmp/firewall_backup_{{ ansible_date_time.date }}"
    
    # 端口配置
    open_ports: "{{ open_ports | default([]) }}"  # 例如: ['80/tcp', '443/tcp', '53/udp']
    close_ports: "{{ close_ports | default([]) }}"
    
    # 服务配置
    enable_services: "{{ enable_services | default([]) }}"  # 例如: ['http', 'https', 'ssh']
    disable_services: "{{ disable_services | default([]) }}"
    
    # 源地址配置
    allow_sources: "{{ allow_sources | default([]) }}"  # 例如: ['***********/24', '10.0.0.0/8']
    block_sources: "{{ block_sources | default([]) }}"
    
    # 富规则配置
    rich_rules: "{{ rich_rules | default([]) }}"  # 例如: ['rule family="ipv4" source address="************" port port="22" protocol="tcp" accept']
    remove_rich_rules: "{{ remove_rich_rules | default([]) }}"
    
    # 转发配置
    forward_ports: "{{ forward_ports | default([]) }}"  # 例如: ['port=80:proto=tcp:toport=8080:toaddr=************']
    remove_forward_ports: "{{ remove_forward_ports | default([]) }}"
    
    # 伪装配置
    enable_masquerade: "{{ enable_masquerade | default(false) }}"
    
    # ICMP 配置
    icmp_blocks: "{{ icmp_blocks | default([]) }}"  # 例如: ['echo-request', 'echo-reply']
    remove_icmp_blocks: "{{ remove_icmp_blocks | default([]) }}"

  tasks:
    - name: 确保 firewalld 已安装
      package:
        name: firewalld
        state: present
      
    - name: 确保 firewalld 服务已启动并设置为开机自启
      service:
        name: firewalld
        state: started
        enabled: yes
      
    - name: 创建备份目录
      file:
        path: "{{ backup_dir }}"
        state: directory
        mode: '0755'
      
    - name: 备份当前防火墙配置
      shell: firewall-cmd --list-all-zones > {{ backup_dir }}/firewalld_all_zones_before.txt
      
    # 端口管理
    - name: 开放端口
      firewalld:
        port: "{{ item }}"
        zone: "{{ firewalld_zone }}"
        permanent: "{{ permanent }}"
        state: enabled
        immediate: yes
      with_items: "{{ open_ports }}"
      when: open_ports | length > 0
      notify: 重载防火墙规则
      
    - name: 关闭端口
      firewalld:
        port: "{{ item }}"
        zone: "{{ firewalld_zone }}"
        permanent: "{{ permanent }}"
        state: disabled
        immediate: yes
      with_items: "{{ close_ports }}"
      when: close_ports | length > 0
      notify: 重载防火墙规则
      
    # 服务管理
    - name: 启用服务
      firewalld:
        service: "{{ item }}"
        zone: "{{ firewalld_zone }}"
        permanent: "{{ permanent }}"
        state: enabled
        immediate: yes
      with_items: "{{ enable_services }}"
      when: enable_services | length > 0
      notify: 重载防火墙规则
      
    - name: 禁用服务
      firewalld:
        service: "{{ item }}"
        zone: "{{ firewalld_zone }}"
        permanent: "{{ permanent }}"
        state: disabled
        immediate: yes
      with_items: "{{ disable_services }}"
      when: disable_services | length > 0
      notify: 重载防火墙规则
      
    # 源地址管理
    - name: 允许源地址
      firewalld:
        source: "{{ item }}"
        zone: "{{ firewalld_zone }}"
        permanent: "{{ permanent }}"
        state: enabled
        immediate: yes
      with_items: "{{ allow_sources }}"
      when: allow_sources | length > 0
      notify: 重载防火墙规则
      
    - name: 阻止源地址
      firewalld:
        source: "{{ item }}"
        zone: "{{ firewalld_zone }}"
        permanent: "{{ permanent }}"
        state: disabled
        immediate: yes
      with_items: "{{ block_sources }}"
      when: block_sources | length > 0
      notify: 重载防火墙规则
      
    # 富规则管理
    - name: 添加富规则
      firewalld:
        rich_rule: "{{ item }}"
        zone: "{{ firewalld_zone }}"
        permanent: "{{ permanent }}"
        state: enabled
        immediate: yes
      with_items: "{{ rich_rules }}"
      when: rich_rules | length > 0
      notify: 重载防火墙规则
      
    - name: 删除富规则
      firewalld:
        rich_rule: "{{ item }}"
        zone: "{{ firewalld_zone }}"
        permanent: "{{ permanent }}"
        state: disabled
        immediate: yes
      with_items: "{{ remove_rich_rules }}"
      when: remove_rich_rules | length > 0
      notify: 重载防火墙规则
      
    # 端口转发管理
    - name: 添加端口转发
      firewalld:
        forward_port: "{{ item }}"
        zone: "{{ firewalld_zone }}"
        permanent: "{{ permanent }}"
        state: enabled
        immediate: yes
      with_items: "{{ forward_ports }}"
      when: forward_ports | length > 0
      notify: 重载防火墙规则
      
    - name: 删除端口转发
      firewalld:
        forward_port: "{{ item }}"
        zone: "{{ firewalld_zone }}"
        permanent: "{{ permanent }}"
        state: disabled
        immediate: yes
      with_items: "{{ remove_forward_ports }}"
      when: remove_forward_ports | length > 0
      notify: 重载防火墙规则
      
    # 伪装配置
    - name: 配置伪装
      firewalld:
        masquerade: "{{ enable_masquerade | string }}"
        zone: "{{ firewalld_zone }}"
        permanent: "{{ permanent }}"
        state: enabled
        immediate: yes
      when: enable_masquerade is defined
      notify: 重载防火墙规则
      
    # ICMP 配置
    - name: 阻止 ICMP 类型
      firewalld:
        icmp_block: "{{ item }}"
        zone: "{{ firewalld_zone }}"
        permanent: "{{ permanent }}"
        state: enabled
        immediate: yes
      with_items: "{{ icmp_blocks }}"
      when: icmp_blocks | length > 0
      notify: 重载防火墙规则
      
    - name: 允许 ICMP 类型
      firewalld:
        icmp_block: "{{ item }}"
        zone: "{{ firewalld_zone }}"
        permanent: "{{ permanent }}"
        state: disabled
        immediate: yes
      with_items: "{{ remove_icmp_blocks }}"
      when: remove_icmp_blocks | length > 0
      notify: 重载防火墙规则
      
    # 保存配置
    - name: 保存防火墙配置为永久配置
      command: firewall-cmd --runtime-to-permanent
      when: permanent | bool
      
    # 验证配置
    - name: 验证防火墙配置
      shell: firewall-cmd --zone={{ firewalld_zone }} --list-all
      register: firewall_config
      
    - name: 显示防火墙配置
      debug:
        msg: "{{ firewall_config.stdout_lines }}"
        
    - name: 备份更新后的防火墙配置
      shell: firewall-cmd --list-all-zones > {{ backup_dir }}/firewalld_all_zones_after.txt

  handlers:
    - name: 重载防火墙规则
      command: firewall-cmd --reload
      
    - name: 完全重载防火墙规则
      command: firewall-cmd --complete-reload
      
    - name: 重启防火墙服务
      service:
        name: firewalld
        state: restarted
