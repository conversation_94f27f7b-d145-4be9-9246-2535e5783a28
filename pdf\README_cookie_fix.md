# PDF.js Cookie 问题修复指南

## 问题描述

您的 cookie `Route-LB=2.c8.9619d032.50` 在业务中能正常访问，但在 pdf.js 中访问有问题。

## 解决方案

我已经创建了两个修复版本：

### 1. pdf_fixed.js - 修复版本
这是您原始 pdf.js 的修复版本，主要改进：
- 正确设置 Cookie 头（注意大小写）
- 使用 phantom.addCookie 方法
- 添加详细的调试日志
- 改进错误处理

### 2. test_cookie.js - 调试工具
用于测试和调试 cookie 问题的专用脚本。

## 使用方法

### 测试 Cookie 是否正常工作

```bash
phantomjs test_cookie.js "http://your-domain.com/your-page" "Route-LB=2.c8.9619d032.50"
```

这个命令会显示详细的调试信息，包括：
- Cookie 设置过程
- HTTP 请求头
- 服务器响应
- 任何错误信息

### 使用修复版本生成 PDF

```bash
phantomjs pdf_fixed.js "http://your-domain.com/your-page" "output.pdf" "Route-LB=2.c8.9619d032.50"
```

## 主要修复点

### 1. Cookie 头设置
```javascript
// 原来的问题：小写 'cookie'
page.customHeaders = {'cookie': cookie};

// 修复：正确的大写 'Cookie'
page.customHeaders = {'Cookie': cookie};
```

### 2. 域名提取和 Cookie 添加
```javascript
// 正确提取域名
var domain = extractDomain(address);

// 使用 phantom.addCookie 方法
phantom.addCookie({
    'name': cookieName,
    'value': cookieValue,
    'domain': domain,
    'path': '/',
    'httponly': false,
    'secure': false
});
```

### 3. 添加调试信息
- 显示 Cookie 设置过程
- 监听 HTTP 请求和响应
- 显示错误信息

## 常见问题排查

### 1. 检查 Cookie 格式
确保您的 cookie 格式正确：
```
Route-LB=2.c8.9619d032.50
```

### 2. 检查域名匹配
Cookie 的域名必须与访问的 URL 匹配。使用 test_cookie.js 可以看到提取的域名。

### 3. 检查服务器响应
如果服务器返回 4xx 或 5xx 错误，可能是：
- Cookie 无效或过期
- 服务器不接受该 Cookie
- 需要额外的请求头

### 4. 负载均衡器问题
您的 cookie `Route-LB` 看起来是负载均衡器的路由 cookie。可能的问题：
- 负载均衡器配置变更
- Cookie 值过期
- 需要特定的请求路径

## 调试步骤

1. **首先运行测试脚本**：
   ```bash
   phantomjs test_cookie.js "http://your-domain.com" "Route-LB=2.c8.9619d032.50"
   ```

2. **检查输出中的关键信息**：
   - Cookie 是否正确设置
   - HTTP 请求是否包含 Cookie 头
   - 服务器响应状态码
   - 任何错误信息

3. **如果测试成功，使用修复版本**：
   ```bash
   phantomjs pdf_fixed.js "http://your-domain.com/page" "output.pdf" "Route-LB=2.c8.9619d032.50"
   ```

## 可能的解决方案

### 方案1：使用修复版本
直接使用 `pdf_fixed.js` 替换原来的 `pdf.js`。

### 方案2：更新原文件
如果您想保持原文件，只需要修改第 34 行：
```javascript
// 将这行：
page.customHeaders = {'cookie': cookie};

// 改为：
page.customHeaders = {'Cookie': cookie};
```

### 方案3：添加更多请求头
有时候需要添加更多的请求头来模拟真实浏览器：
```javascript
page.customHeaders = {
    'Cookie': cookie,
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
    'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
    'Accept-Encoding': 'gzip, deflate'
};
```

## 联系支持

如果问题仍然存在，请提供：
1. test_cookie.js 的完整输出
2. 您访问的完整 URL
3. 服务器返回的错误信息
4. 浏览器中正常访问时的 Network 面板截图
