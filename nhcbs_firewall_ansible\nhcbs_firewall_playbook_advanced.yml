---
# nhcbs_firewall_playbook_advanced.yml
# 用于部署和执行 nhcbs 防火墙配置脚本的高级 Ansible Playbook
# 专注于 firewalld 防火墙管理

- name: 部署并执行 NHCBS 防火墙配置 (firewalld)
  hosts: "{{ target_hosts | default('all') }}"  # 可通过变量指定目标主机
  become: true  # 使用 sudo 权限执行
  gather_facts: true  # 收集目标主机信息

  vars:
    script_url: "http://fd.ops.public.private.com/nhcbs/nhcbs-firewall.sh"
    temp_script_path: "/tmp/nhcbs-firewall.sh"
    backup_dir: "/tmp/firewall_backup_{{ ansible_date_time.date }}"
    max_retries: 3
    retry_delay: 5
    timeout: 300
    debug_mode: true
    backup_config: true
    verify_after_run: true
    firewalld_zone: "{{ firewalld_zone | default('public') }}"  # 默认防火墙区域
    permanent: "{{ permanent | default(true) }}"  # 默认永久规则

  pre_tasks:
    - name: 检查网络连接
      wait_for:
        host: "{{ script_url | urlsplit('hostname') }}"
        port: 80
        timeout: 10
      delegate_to: localhost
      register: network_check
      ignore_errors: yes

    - name: 显示网络连接状态
      debug:
        msg: "网络连接状态: {{ 'OK' if network_check is succeeded else '失败' }}"

    - name: 中止任务如果网络连接失败
      fail:
        msg: "无法连接到脚本服务器，请检查网络连接"
      when: network_check is failed and not ignore_network_errors | default(false)

  tasks:
    - name: 创建备份目录
      file:
        path: "{{ backup_dir }}"
        state: directory
        mode: '0755'
      when: backup_config | bool

    - name: 检查 firewalld 是否安装
      command: which firewall-cmd
      register: firewalld_check
      ignore_errors: yes

    - name: 确保 firewalld 服务已安装
      package:
        name: firewalld
        state: present
      when: firewalld_check is failed

    - name: 确保 firewalld 服务已启动并设置为开机自启
      service:
        name: firewalld
        state: started
        enabled: yes

    - name: 备份当前防火墙配置
      block:
        - name: 备份所有区域配置
          shell: firewall-cmd --list-all-zones > {{ backup_dir }}/firewalld_all_zones.txt

        - name: 备份默认区域配置
          shell: firewall-cmd --list-all > {{ backup_dir }}/firewalld_default_zone.txt

        - name: 备份指定区域配置
          shell: firewall-cmd --zone={{ firewalld_zone }} --list-all > {{ backup_dir }}/firewalld_{{ firewalld_zone }}_zone.txt

        - name: 备份服务配置
          shell: firewall-cmd --get-services > {{ backup_dir }}/firewalld_services.txt

        - name: 备份端口配置
          shell: firewall-cmd --zone={{ firewalld_zone }} --list-ports > {{ backup_dir }}/firewalld_ports.txt

        - name: 备份防火墙规则
          shell: firewall-cmd --direct --get-all-rules > {{ backup_dir }}/firewalld_direct_rules.txt
      ignore_errors: yes
      when: backup_config | bool

    - name: 下载防火墙配置脚本
      get_url:
        url: "{{ script_url }}"
        dest: "{{ temp_script_path }}"
        mode: '0755'
        force: yes
        timeout: 30
      register: download_result
      retries: "{{ max_retries }}"
      delay: "{{ retry_delay }}"
      until: download_result is succeeded

    - name: 检查脚本是否成功下载
      stat:
        path: "{{ temp_script_path }}"
      register: script_file

    - name: 验证脚本内容
      shell: grep -q "firewall" {{ temp_script_path }}
      register: script_validation
      ignore_errors: yes
      when: script_file.stat.exists

    - name: 显示脚本验证结果
      debug:
        msg: "脚本验证: {{ '通过' if script_validation is succeeded else '失败' }}"
      when: script_file.stat.exists

    - name: 执行防火墙配置脚本（带调试输出）
      shell: bash -x {{ temp_script_path }}
      register: script_output
      failed_when: script_output.rc != 0
      changed_when: script_output.rc == 0
      async: "{{ timeout }}"
      poll: 5
      when: script_file.stat.exists and (script_validation is succeeded or ignore_validation_errors | default(false))

    - name: 显示脚本执行输出
      debug:
        msg: "{{ script_output.stdout_lines }}"
      when: debug_mode | bool and script_output.stdout_lines is defined

    - name: 显示脚本执行错误（如果有）
      debug:
        msg: "{{ script_output.stderr_lines }}"
      when: debug_mode | bool and script_output.stderr_lines is defined and script_output.stderr_lines | length > 0

    - name: 删除临时脚本文件
      file:
        path: "{{ temp_script_path }}"
        state: absent

    - name: 验证防火墙配置
      block:
        - name: 检查 firewalld 服务状态
          shell: firewall-cmd --state
          register: firewalld_state

        - name: 检查默认区域
          shell: firewall-cmd --get-default-zone
          register: default_zone

        - name: 检查指定区域配置
          shell: firewall-cmd --zone={{ firewalld_zone }} --list-all
          register: zone_config

        - name: 检查活动区域
          shell: firewall-cmd --get-active-zones
          register: active_zones

        - name: 检查运行时配置
          shell: firewall-cmd --runtime-to-permanent
          register: runtime_config
          when: permanent | bool

        - name: 检查防火墙是否重载成功
          shell: firewall-cmd --reload
          register: reload_result
      ignore_errors: yes
      when: verify_after_run | bool

    - name: 显示防火墙验证结果
      debug:
        msg: |
          防火墙状态: {{ firewalld_state.stdout if firewalld_state.stdout is defined else '未知' }}
          默认区域: {{ default_zone.stdout if default_zone.stdout is defined else '未知' }}
          活动区域: {{ active_zones.stdout if active_zones.stdout is defined else '未知' }}
          {{ firewalld_zone }} 区域配置:
          {{ zone_config.stdout if zone_config.stdout is defined else '未知' }}
      when: verify_after_run | bool

    - name: 确认防火墙配置完成
      debug:
        msg: "NHCBS 防火墙配置已完成"

    - name: 保存防火墙配置为永久配置
      command: firewall-cmd --runtime-to-permanent
      when: permanent | bool

  handlers:
    - name: 重启防火墙服务
      service:
        name: firewalld
        state: restarted

    - name: 重载防火墙规则
      command: firewall-cmd --reload

    - name: 完全重载防火墙规则
      command: firewall-cmd --complete-reload
