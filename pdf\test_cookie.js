"use strict";
var page = require('webpage').create(),
    system = require('system');

if (system.args.length < 2) {
    console.log('Usage: test_cookie.js URL [cookie]');
    phantom.exit(1);
}

var address = system.args[1];
var cookie = system.args[2];

console.log('Testing URL: ' + address);
console.log('Testing Cookie: ' + cookie);

// 辅助函数：从 URL 中提取域名
function extractDomain(url) {
    var domain;
    try {
        if (url.indexOf("://") > -1) {
            domain = url.split('/')[2];
        } else {
            domain = url.split('/')[0];
        }
        domain = domain.split(':')[0];
        return domain;
    } catch (e) {
        console.log('Error extracting domain: ' + e.message);
        return 'localhost';
    }
}

// 设置 User-Agent
page.settings.userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36';

if (cookie) {
    console.log('\n=== Cookie Setup ===');
    
    // 方法1: 设置 Cookie 头
    page.customHeaders = {
        'Cookie': cookie
    };
    console.log('Set Cookie header: ' + cookie);
    
    // 方法2: 使用 phantom.addCookie
    var domain = extractDomain(address);
    console.log('Target domain: ' + domain);
    
    var cookieParts = cookie.split(';');
    for (var i = 0; i < cookieParts.length; i++) {
        var cookiePart = cookieParts[i].trim();
        if (cookiePart) {
            var equalIndex = cookiePart.indexOf('=');
            if (equalIndex > 0) {
                var cookieName = cookiePart.substring(0, equalIndex).trim();
                var cookieValue = cookiePart.substring(equalIndex + 1).trim();
                
                console.log('Adding cookie: ' + cookieName + ' = ' + cookieValue);
                
                var success = phantom.addCookie({
                    'name': cookieName,
                    'value': cookieValue,
                    'domain': domain,
                    'path': '/',
                    'httponly': false,
                    'secure': false
                });
                
                console.log('Cookie add success: ' + success);
            }
        }
    }
    
    // 验证设置的 cookies
    var cookies = phantom.cookies;
    console.log('\nCurrent cookies (' + cookies.length + '):');
    for (var j = 0; j < cookies.length; j++) {
        console.log('  ' + cookies[j].name + ' = ' + cookies[j].value + ' (domain: ' + cookies[j].domain + ')');
    }
}

// 监听请求
page.onResourceRequested = function(requestData, networkRequest) {
    console.log('\n=== Request ===');
    console.log('URL: ' + requestData.url);
    console.log('Method: ' + requestData.method);
    
    if (requestData.headers) {
        console.log('Headers:');
        for (var k = 0; k < requestData.headers.length; k++) {
            var header = requestData.headers[k];
            console.log('  ' + header.name + ': ' + header.value);
        }
    }
};

// 监听响应
page.onResourceReceived = function(response) {
    if (response.stage === 'start') {
        console.log('\n=== Response ===');
        console.log('URL: ' + response.url);
        console.log('Status: ' + response.status + ' ' + response.statusText);
        
        if (response.headers) {
            console.log('Response Headers:');
            for (var i = 0; i < response.headers.length; i++) {
                var header = response.headers[i];
                console.log('  ' + header.name + ': ' + header.value);
            }
        }
    }
};

// 监听错误
page.onError = function(msg, trace) {
    console.log('\n=== Page Error ===');
    console.log('Error: ' + msg);
    if (trace && trace.length) {
        console.log('Trace:');
        trace.forEach(function(t) {
            console.log('  -> ' + (t.file || t.sourceURL) + ': ' + t.line);
        });
    }
};

page.onResourceError = function(resourceError) {
    console.log('\n=== Resource Error ===');
    console.log('URL: ' + resourceError.url);
    console.log('Error: ' + resourceError.errorString);
    console.log('Code: ' + resourceError.errorCode);
};

// 打开页面
console.log('\n=== Opening Page ===');
page.open(address, function (status) {
    console.log('\n=== Page Open Result ===');
    console.log('Status: ' + status);
    
    if (status === 'success') {
        console.log('Page loaded successfully');
        
        // 获取页面标题
        var title = page.evaluate(function() {
            return document.title;
        });
        console.log('Page title: ' + title);
        
        // 检查页面内容
        var bodyText = page.evaluate(function() {
            return document.body ? document.body.innerText.substring(0, 200) : 'No body content';
        });
        console.log('Page content preview: ' + bodyText);
        
    } else {
        console.log('Failed to load page');
    }
    
    phantom.exit(status === 'success' ? 0 : 1);
});
