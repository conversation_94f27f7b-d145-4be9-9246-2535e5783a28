package main

import (
	"fmt"
	"io"
	"log"
	"os"
	"strings"
	"time"
)

// LogLevel 日志级别
type LogLevel int

const (
	DEBUG LogLevel = iota
	INFO
	WARN
	ERROR
)

// String 返回日志级别的字符串表示
func (l LogLevel) String() string {
	switch l {
	case DEBUG:
		return "DEBUG"
	case INFO:
		return "INFO"
	case WARN:
		return "WARN"
	case ERROR:
		return "ERROR"
	default:
		return "UNKNOWN"
	}
}

// Logger 日志记录器
type Logger struct {
	level   LogLevel
	logger  *log.Logger
	file    *os.File
	console bool
}

// NewLogger 创建新的日志记录器
func NewLogger(config LoggingConfig) (*Logger, error) {
	level := parseLogLevel(config.Level)
	
	var writers []io.Writer
	var file *os.File
	var err error

	// 如果指定了日志文件，添加文件输出
	if config.File != "" {
		file, err = os.OpenFile(config.File, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			return nil, fmt.Errorf("打开日志文件失败: %v", err)
		}
		writers = append(writers, file)
	}

	// 如果启用控制台输出，添加标准输出
	if config.Console {
		writers = append(writers, os.Stdout)
	}

	// 如果没有任何输出，默认使用标准输出
	if len(writers) == 0 {
		writers = append(writers, os.Stdout)
	}

	// 创建多重写入器
	multiWriter := io.MultiWriter(writers...)
	
	// 创建日志记录器
	logger := log.New(multiWriter, "", 0)

	return &Logger{
		level:   level,
		logger:  logger,
		file:    file,
		console: config.Console,
	}, nil
}

// parseLogLevel 解析日志级别
func parseLogLevel(level string) LogLevel {
	switch strings.ToUpper(level) {
	case "DEBUG":
		return DEBUG
	case "INFO":
		return INFO
	case "WARN", "WARNING":
		return WARN
	case "ERROR":
		return ERROR
	default:
		return INFO
	}
}

// formatMessage 格式化日志消息
func (l *Logger) formatMessage(level LogLevel, msg string) string {
	timestamp := time.Now().Format("2006-01-02 15:04:05")
	return fmt.Sprintf("[%s] %s - %s", timestamp, level.String(), msg)
}

// log 记录日志
func (l *Logger) log(level LogLevel, msg string) {
	if level >= l.level {
		l.logger.Println(l.formatMessage(level, msg))
	}
}

// Debug 记录调试日志
func (l *Logger) Debug(msg string) {
	l.log(DEBUG, msg)
}

// Debugf 记录格式化调试日志
func (l *Logger) Debugf(format string, args ...interface{}) {
	l.log(DEBUG, fmt.Sprintf(format, args...))
}

// Info 记录信息日志
func (l *Logger) Info(msg string) {
	l.log(INFO, msg)
}

// Infof 记录格式化信息日志
func (l *Logger) Infof(format string, args ...interface{}) {
	l.log(INFO, fmt.Sprintf(format, args...))
}

// Warn 记录警告日志
func (l *Logger) Warn(msg string) {
	l.log(WARN, msg)
}

// Warnf 记录格式化警告日志
func (l *Logger) Warnf(format string, args ...interface{}) {
	l.log(WARN, fmt.Sprintf(format, args...))
}

// Error 记录错误日志
func (l *Logger) Error(msg string) {
	l.log(ERROR, msg)
}

// Errorf 记录格式化错误日志
func (l *Logger) Errorf(format string, args ...interface{}) {
	l.log(ERROR, fmt.Sprintf(format, args...))
}

// Close 关闭日志记录器
func (l *Logger) Close() error {
	if l.file != nil {
		return l.file.Close()
	}
	return nil
}
