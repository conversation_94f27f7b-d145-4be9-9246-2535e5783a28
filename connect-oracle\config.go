package main

import (
	"fmt"
	"os"
	"path/filepath"

	"gopkg.in/yaml.v3"
)

// Config 配置结构体
type Config struct {
	Database   DatabaseConfig   `yaml:"database"`
	Connection ConnectionConfig `yaml:"connection"`
	Query      QueryConfig      `yaml:"query"`
	Logging    LoggingConfig    `yaml:"logging"`
	SQL        SQLConfig        `yaml:"sql"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host        string `yaml:"host"`
	Port        int    `yaml:"port"`
	ServiceName string `yaml:"service_name"`
	Username    string `yaml:"username"`
	Password    string `yaml:"password"`
}

// ConnectionConfig 连接配置
type ConnectionConfig struct {
	Timeout         int `yaml:"timeout"`
	MaxIdleConns    int `yaml:"max_idle_conns"`
	MaxOpenConns    int `yaml:"max_open_conns"`
	ConnMaxLifetime int `yaml:"conn_max_lifetime"`
}

// QueryConfig 查询配置
type QueryConfig struct {
	Timeout int `yaml:"timeout"`
}

// LoggingConfig 日志配置
type LoggingConfig struct {
	Level   string `yaml:"level"`
	File    string `yaml:"file"`
	Console bool   `yaml:"console"`
}

// SQLConfig SQL 配置
type SQLConfig struct {
	MainQuery      string `yaml:"main_query"`
	TestConnection string `yaml:"test_connection"`
	CheckTables    string `yaml:"check_tables"`
	TableInfo      string `yaml:"table_info"`
}

// GetDSN 获取数据库连接字符串
func (d *DatabaseConfig) GetDSN() string {
	return fmt.Sprintf("%s/%s@%s:%d/%s",
		d.Username, d.Password, d.Host, d.Port, d.ServiceName)
}

// LoadConfig 加载配置文件
func LoadConfig(configPath string) (*Config, error) {
	// 如果没有指定配置文件路径，使用默认路径
	if configPath == "" {
		configPath = "config.yaml"
	}

	// 检查配置文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("配置文件不存在: %s", configPath)
	}

	// 读取配置文件
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}

	// 解析 YAML
	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 验证配置
	if err := validateConfig(&config); err != nil {
		return nil, fmt.Errorf("配置验证失败: %v", err)
	}

	return &config, nil
}

// validateConfig 验证配置
func validateConfig(config *Config) error {
	if config.Database.Host == "" {
		return fmt.Errorf("数据库主机地址不能为空")
	}
	if config.Database.Port <= 0 {
		return fmt.Errorf("数据库端口必须大于0")
	}
	if config.Database.Username == "" {
		return fmt.Errorf("数据库用户名不能为空")
	}
	if config.Database.Password == "" {
		return fmt.Errorf("数据库密码不能为空")
	}
	if config.Database.ServiceName == "" {
		return fmt.Errorf("数据库服务名不能为空")
	}
	if config.SQL.MainQuery == "" {
		return fmt.Errorf("主查询SQL不能为空")
	}
	return nil
}

// SaveDefaultConfig 保存默认配置文件
func SaveDefaultConfig(configPath string) error {
	defaultConfig := &Config{
		Database: DatabaseConfig{
			Host:        "********",
			Port:        1521,
			ServiceName: "ORCL",
			Username:    "admin",
			Password:    "pass",
		},
		Connection: ConnectionConfig{
			Timeout:         30,
			MaxIdleConns:    2,
			MaxOpenConns:    10,
			ConnMaxLifetime: 300,
		},
		Query: QueryConfig{
			Timeout: 60,
		},
		Logging: LoggingConfig{
			Level:   "info",
			File:    "oracle_query.log",
			Console: true,
		},
		SQL: SQLConfig{
			MainQuery: `SELECT COUNT(a.id) as total_count
FROM bas_instation_task a
INNER JOIN bas_instation_task_detail b ON a.msg_id = b.msg_id
LEFT JOIN bas_user c ON b.send_user_id = c.user_id
LEFT JOIN (
    SELECT * FROM BAS_CONFIG_value WHERE key_id = 49
) d ON d.value_code = b.bus_type
WHERE a.task_Status IN (10, 20)
AND a.receive_user_id = 509548
AND (b.app_show = 1 OR b.app_show IS NULL)`,
			TestConnection: "SELECT 1 FROM DUAL",
			CheckTables: `SELECT table_name 
FROM user_tables 
WHERE table_name IN ('BAS_INSTATION_TASK', 'BAS_INSTATION_TASK_DETAIL', 'BAS_USER', 'BAS_CONFIG_VALUE')`,
			TableInfo: `SELECT table_name, column_name, data_type, nullable
FROM user_tab_columns 
WHERE table_name IN ('BAS_INSTATION_TASK', 'BAS_INSTATION_TASK_DETAIL', 'BAS_USER', 'BAS_CONFIG_VALUE')
ORDER BY table_name, column_id`,
		},
	}

	// 确保目录存在
	dir := filepath.Dir(configPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建目录失败: %v", err)
	}

	// 序列化为 YAML
	data, err := yaml.Marshal(defaultConfig)
	if err != nil {
		return fmt.Errorf("序列化配置失败: %v", err)
	}

	// 写入文件
	if err := os.WriteFile(configPath, data, 0644); err != nil {
		return fmt.Errorf("写入配置文件失败: %v", err)
	}

	return nil
}
