@echo off
setlocal enabledelayedexpansion

REM Oracle Query Tool 构建脚本 (Windows)

set APP_NAME=oracle-query
set VERSION=1.0.0

echo Oracle Query Tool 构建脚本 v%VERSION%
echo ==================================

REM 检查 Go 是否安装
where go >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到 Go 编译器，请先安装 Go
    exit /b 1
)

echo Go 版本:
go version

REM 启用 CGO
set CGO_ENABLED=1

REM 创建构建目录
set BUILD_DIR=build
if not exist %BUILD_DIR% mkdir %BUILD_DIR%

REM 下载依赖
echo 正在下载依赖...
go mod download
go mod tidy

REM 构建应用程序
echo 正在构建应用程序...

set BINARY_NAME=%APP_NAME%.exe
set LDFLAGS=-ldflags "-X main.Version=%VERSION% -X main.AppName='Oracle Query Tool'"

echo 目标平台: windows/amd64
echo 二进制文件: %BUILD_DIR%\%BINARY_NAME%

REM 构建
go build %LDFLAGS% -o %BUILD_DIR%\%BINARY_NAME% .

if %errorlevel% equ 0 (
    echo 构建成功!
    echo 二进制文件位置: %BUILD_DIR%\%BINARY_NAME%
    
    REM 复制配置文件
    if exist config.yaml (
        copy config.yaml %BUILD_DIR%\
        echo 配置文件已复制到构建目录
    )
    
    REM 显示文件信息
    echo.
    echo 构建结果:
    dir %BUILD_DIR%
    
    echo.
    echo 使用方法:
    echo   cd %BUILD_DIR%
    echo   %BINARY_NAME% -help
    
) else (
    echo 构建失败!
    exit /b 1
)

pause
