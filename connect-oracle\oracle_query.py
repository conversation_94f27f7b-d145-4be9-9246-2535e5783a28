#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Oracle 数据库查询脚本
用于连接 Oracle 数据库并执行指定的查询
"""

import cx_Oracle
import logging
import sys
import os
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("oracle_query.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class OracleConnector:
    """Oracle 数据库连接器"""
    
    def __init__(self, host, port, service_name, username, password):
        """
        初始化数据库连接参数
        
        Args:
            host: 数据库主机地址
            port: 数据库端口
            service_name: 服务名
            username: 用户名
            password: 密码
        """
        self.host = host
        self.port = port
        self.service_name = service_name
        self.username = username
        self.password = password
        self.connection = None
        self.cursor = None
        
    def connect(self):
        """连接到 Oracle 数据库"""
        try:
            # 构建连接字符串
            dsn = cx_Oracle.makedsn(self.host, self.port, service_name=self.service_name)
            logger.info(f"正在连接到 Oracle 数据库: {self.host}:{self.port}/{self.service_name}")
            
            # 建立连接
            self.connection = cx_Oracle.connect(
                user=self.username,
                password=self.password,
                dsn=dsn,
                encoding="UTF-8"
            )
            
            # 创建游标
            self.cursor = self.connection.cursor()
            logger.info("数据库连接成功")
            return True
            
        except cx_Oracle.Error as e:
            logger.error(f"数据库连接失败: {e}")
            return False
        except Exception as e:
            logger.error(f"连接时发生未知错误: {e}")
            return False
    
    def execute_query(self, sql):
        """
        执行查询语句
        
        Args:
            sql: SQL 查询语句
            
        Returns:
            查询结果
        """
        try:
            if not self.cursor:
                logger.error("数据库未连接")
                return None
            
            logger.info("执行查询语句:")
            logger.info(sql)
            
            # 执行查询
            start_time = datetime.now()
            self.cursor.execute(sql)
            
            # 获取结果
            results = self.cursor.fetchall()
            end_time = datetime.now()
            
            execution_time = (end_time - start_time).total_seconds()
            logger.info(f"查询执行完成，耗时: {execution_time:.2f} 秒")
            
            return results
            
        except cx_Oracle.Error as e:
            logger.error(f"查询执行失败: {e}")
            return None
        except Exception as e:
            logger.error(f"查询时发生未知错误: {e}")
            return None
    
    def get_column_names(self):
        """获取查询结果的列名"""
        if self.cursor and self.cursor.description:
            return [desc[0] for desc in self.cursor.description]
        return []
    
    def close(self):
        """关闭数据库连接"""
        try:
            if self.cursor:
                self.cursor.close()
                logger.info("游标已关闭")
            
            if self.connection:
                self.connection.close()
                logger.info("数据库连接已关闭")
                
        except Exception as e:
            logger.error(f"关闭连接时发生错误: {e}")

def main():
    """主函数"""
    # 数据库连接配置
    DB_CONFIG = {
        'host': '********',
        'port': 1521,
        'service_name': 'ORCL',  # 默认服务名，可能需要根据实际情况调整
        'username': 'admin',
        'password': 'pass'
    }
    
    # SQL 查询语句
    SQL_QUERY = """
    SELECT COUNT(a.id) as total_count
    FROM bas_instation_task a
    INNER JOIN bas_instation_task_detail b ON a.msg_id = b.msg_id
    LEFT JOIN bas_user c ON b.send_user_id = c.user_id
    LEFT JOIN (
        SELECT * FROM BAS_CONFIG_value WHERE key_id = 49
    ) d ON d.value_code = b.bus_type
    WHERE a.task_Status IN (10, 20)
    AND a.receive_user_id = 509548
    AND (b.app_show = 1 OR b.app_show IS NULL)
    """
    
    # 创建数据库连接器
    db = OracleConnector(**DB_CONFIG)
    
    try:
        # 连接数据库
        if not db.connect():
            logger.error("无法连接到数据库，程序退出")
            sys.exit(1)
        
        # 执行查询
        results = db.execute_query(SQL_QUERY)
        
        if results is not None:
            # 获取列名
            columns = db.get_column_names()
            
            # 显示结果
            logger.info("查询结果:")
            if columns:
                logger.info(f"列名: {', '.join(columns)}")
            
            if results:
                for row in results:
                    logger.info(f"结果: {row}")
                    if len(row) == 1:
                        logger.info(f"总记录数: {row[0]}")
            else:
                logger.info("查询结果为空")
        else:
            logger.error("查询失败")
            
    except KeyboardInterrupt:
        logger.info("用户中断程序")
    except Exception as e:
        logger.error(f"程序执行时发生错误: {e}")
    finally:
        # 关闭数据库连接
        db.close()

if __name__ == "__main__":
    main()
