# Makefile for Oracle Query Tool

# 应用名称
APP_NAME = oracle-query
VERSION = 1.0.0

# Go 相关变量
GO = go
GOOS ?= $(shell go env GOOS)
GOARCH ?= $(shell go env GOARCH)

# 构建目录
BUILD_DIR = build
DIST_DIR = dist

# 二进制文件名
BINARY_NAME = $(APP_NAME)
ifeq ($(GOOS),windows)
    BINARY_NAME = $(APP_NAME).exe
endif

# 构建标志
LDFLAGS = -ldflags "-X main.Version=$(VERSION) -X main.AppName='Oracle Query Tool'"

# 默认目标
.PHONY: all
all: clean build

# 清理
.PHONY: clean
clean:
	@echo "清理构建文件..."
	@rm -rf $(BUILD_DIR) $(DIST_DIR)
	@$(GO) clean

# 下载依赖
.PHONY: deps
deps:
	@echo "下载依赖..."
	@$(GO) mod download
	@$(GO) mod tidy

# 构建
.PHONY: build
build: deps
	@echo "构建应用程序..."
	@mkdir -p $(BUILD_DIR)
	@CGO_ENABLED=1 GOOS=$(GOOS) GOARCH=$(GOARCH) $(GO) build $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME) .
	@echo "构建完成: $(BUILD_DIR)/$(BINARY_NAME)"

# 构建所有平台
.PHONY: build-all
build-all: clean deps
	@echo "构建所有平台版本..."
	@mkdir -p $(DIST_DIR)
	
	# Linux amd64
	@echo "构建 Linux amd64..."
	@CGO_ENABLED=1 GOOS=linux GOARCH=amd64 $(GO) build $(LDFLAGS) -o $(DIST_DIR)/$(APP_NAME)-linux-amd64 .
	
	# Windows amd64
	@echo "构建 Windows amd64..."
	@CGO_ENABLED=1 GOOS=windows GOARCH=amd64 $(GO) build $(LDFLAGS) -o $(DIST_DIR)/$(APP_NAME)-windows-amd64.exe .
	
	# macOS amd64
	@echo "构建 macOS amd64..."
	@CGO_ENABLED=1 GOOS=darwin GOARCH=amd64 $(GO) build $(LDFLAGS) -o $(DIST_DIR)/$(APP_NAME)-darwin-amd64 .
	
	@echo "所有平台构建完成"

# 运行
.PHONY: run
run: build
	@echo "运行应用程序..."
	@cd $(BUILD_DIR) && ./$(BINARY_NAME) -help

# 测试
.PHONY: test
test:
	@echo "运行测试..."
	@$(GO) test -v ./...

# 格式化代码
.PHONY: fmt
fmt:
	@echo "格式化代码..."
	@$(GO) fmt ./...

# 检查代码
.PHONY: vet
vet:
	@echo "检查代码..."
	@$(GO) vet ./...

# 生成配置文件
.PHONY: config
config: build
	@echo "生成默认配置文件..."
	@cd $(BUILD_DIR) && ./$(BINARY_NAME) -gen-config

# 安装
.PHONY: install
install: build
	@echo "安装到 GOPATH/bin..."
	@$(GO) install $(LDFLAGS) .

# 打包
.PHONY: package
package: build-all
	@echo "打包发布文件..."
	@mkdir -p $(DIST_DIR)/packages
	
	# 复制配置文件
	@cp config.yaml $(DIST_DIR)/
	@cp README.md $(DIST_DIR)/
	
	# Linux 包
	@tar -czf $(DIST_DIR)/packages/$(APP_NAME)-$(VERSION)-linux-amd64.tar.gz -C $(DIST_DIR) $(APP_NAME)-linux-amd64 config.yaml README.md
	
	# Windows 包
	@zip -j $(DIST_DIR)/packages/$(APP_NAME)-$(VERSION)-windows-amd64.zip $(DIST_DIR)/$(APP_NAME)-windows-amd64.exe $(DIST_DIR)/config.yaml $(DIST_DIR)/README.md
	
	# macOS 包
	@tar -czf $(DIST_DIR)/packages/$(APP_NAME)-$(VERSION)-darwin-amd64.tar.gz -C $(DIST_DIR) $(APP_NAME)-darwin-amd64 config.yaml README.md
	
	@echo "打包完成，文件位于 $(DIST_DIR)/packages/"

# 帮助
.PHONY: help
help:
	@echo "可用的 make 目标:"
	@echo "  all        - 清理并构建应用程序"
	@echo "  build      - 构建当前平台的应用程序"
	@echo "  build-all  - 构建所有平台的应用程序"
	@echo "  clean      - 清理构建文件"
	@echo "  deps       - 下载依赖"
	@echo "  run        - 构建并运行应用程序"
	@echo "  test       - 运行测试"
	@echo "  fmt        - 格式化代码"
	@echo "  vet        - 检查代码"
	@echo "  config     - 生成默认配置文件"
	@echo "  install    - 安装到 GOPATH/bin"
	@echo "  package    - 打包发布文件"
	@echo "  help       - 显示此帮助信息"
