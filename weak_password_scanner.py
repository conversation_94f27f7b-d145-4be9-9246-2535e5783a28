#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
弱口令密码扫描工具
用于扫描指定网站的弱密码
"""

import requests
import time
import threading
import logging
import argparse
import json
import re
from concurrent.futures import ThreadPoolExecutor
from bs4 import BeautifulSoup

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("password_scan.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 设置调试模式
DEBUG = False

def enable_debug_mode():
    """启用调试模式"""
    global DEBUG
    DEBUG = True
    logger.setLevel(logging.DEBUG)
    # 添加请求库的调试日志
    requests_log = logging.getLogger("requests.packages.urllib3")
    requests_log.setLevel(logging.DEBUG)
    requests_log.propagate = True
    logger.debug("调试模式已启用")

class WeakPasswordScanner:
    """弱口令密码扫描器"""

    def __init__(self, target_url, username=None, password_file=None, threads=5, timeout=10):
        """
        初始化扫描器

        Args:
            target_url: 目标URL
            username: 用户名（如果已知）
            password_file: 密码字典文件路径
            threads: 线程数
            timeout: 请求超时时间
        """
        self.target_url = target_url
        self.username = username
        self.password_file = password_file
        self.threads = threads
        self.timeout = timeout
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.successful_passwords = []
        self.lock = threading.Lock()

        # 如果没有提供密码文件，使用内置的弱密码列表
        self.passwords = self._load_passwords()

    def _load_passwords(self):
        """加载密码列表"""
        default_passwords = [
            "123456", "password", "admin", "admin123", "root", "123456789",
            "qwerty", "12345678", "111111", "1234567890", "123123", "abc123",
            "1234567", "password123", "admin@123", "test", "test123", "123",
            "000000", "666666", "888888", "welcome", "1qaz2wsx", "abc@123",
            "password1", "1234", "12345", "dragon", "qwertyuiop", "monkey",
            "letmein", "baseball", "iloveyou", "trustno1", "sunshine", "master",
            "123qwe", "zxcvbnm", "1q2w3e4r", "superman", "123abc", "654321",
            "1q2w3e", "qwe123", "123456a", "123456789a", "11111111", "1111111"
        ]

        if self.password_file:
            try:
                with open(self.password_file, 'r', encoding='utf-8') as f:
                    return [line.strip() for line in f if line.strip()]
            except Exception as e:
                logger.error(f"无法读取密码文件: {e}")
                logger.info("使用默认密码列表")
                return default_passwords
        else:
            return default_passwords

    def _get_csrf_token(self, html_content):
        """从HTML内容中提取CSRF令牌"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            # 查找可能的CSRF令牌
            # 常见的CSRF令牌字段名
            csrf_fields = ['csrf', 'csrf_token', 'csrfmiddlewaretoken', 'xsrf', '_csrf', 'token']

            for field in csrf_fields:
                # 尝试查找包含这些名称的input字段
                token = soup.find('input', {'name': re.compile(f'.*{field}.*', re.I)})
                if token:
                    return token.get('value')

            # 尝试查找meta标签中的CSRF令牌
            meta_token = soup.find('meta', {'name': re.compile('.*csrf.*', re.I)})
            if meta_token:
                return meta_token.get('content')

            return None
        except Exception as e:
            logger.error(f"提取CSRF令牌时出错: {e}")
            return None

    def _try_login(self, username, password):
        """
        尝试使用给定的用户名和密码登录

        Args:
            username: 用户名
            password: 密码

        Returns:
            bool: 登录是否成功
        """
        try:
            # 首先获取登录页面，可能需要获取CSRF令牌
            response = self.session.get(self.target_url, timeout=self.timeout)
            if response.status_code != 200:
                logger.warning(f"无法访问登录页面，状态码: {response.status_code}")
                return False

            # 记录初始cookies
            initial_cookies = self.session.cookies.get_dict().copy()
            if DEBUG:
                logger.debug(f"初始cookies: {initial_cookies}")
                logger.debug(f"页面内容: {response.text[:500]}...")

            # 提取CSRF令牌（如果有）
            csrf_token = self._get_csrf_token(response.text)
            if csrf_token:
                logger.debug(f"找到CSRF令牌: {csrf_token}")

            # 准备登录数据
            login_data = {}

            # 对于Jupyter，通常只需要密码字段
            login_data['password'] = password

            # 如果有用户名字段，添加用户名
            if username:
                login_data['username'] = username

            # 如果有CSRF令牌，添加到请求中
            if csrf_token:
                login_data['_xsrf'] = csrf_token

            # 记录登录数据
            if DEBUG:
                logger.debug(f"登录数据: {login_data}")

            # 发送登录请求
            login_response = self.session.post(
                self.target_url,
                data=login_data,
                timeout=self.timeout,
                allow_redirects=True
            )

            # 记录响应信息
            if DEBUG:
                logger.debug(f"登录响应状态码: {login_response.status_code}")
                logger.debug(f"登录响应URL: {login_response.url}")
                logger.debug(f"登录后cookies: {self.session.cookies.get_dict()}")
                logger.debug(f"响应内容: {login_response.text[:500]}...")

            # 判断登录是否成功

            # 首先检查状态码，如果是401或403，肯定是失败的
            if login_response.status_code in [401, 403]:
                if DEBUG:
                    logger.debug(f"登录失败 - HTTP状态码: {login_response.status_code}")
                return False

            # 检查重定向URL - 这是最可靠的成功指标
            if '/lab' in login_response.url or '/tree' in login_response.url:
                logger.info(f"登录成功 - 重定向到: {login_response.url}")
                return True

            # 检查是否有错误消息
            error_keywords = ["错误", "Invalid", "incorrect", "failed", "wrong", "失败", "密码错误", "invalid password", "authentication failed"]
            for keyword in error_keywords:
                if keyword.lower() in login_response.text.lower():
                    if DEBUG:
                        logger.debug(f"登录失败 - 发现错误关键词: {keyword}")
                    return False

            # 检查是否有登录成功的标志
            success_keywords = ["Logout", "注销", "退出", "Sign out", "控制台", "Dashboard", "仪表盘", "欢迎", "JupyterLab"]
            for keyword in success_keywords:
                if keyword.lower() in login_response.text.lower():
                    logger.info(f"登录成功 - 发现成功关键词: {keyword}")
                    return True

            # 检查HTTP状态码和URL变化
            if login_response.status_code == 200 and login_response.url != self.target_url:
                # 进一步检查页面内容，确保不是错误页面
                if "error" not in login_response.text.lower() and "invalid" not in login_response.text.lower():
                    logger.info(f"可能登录成功 - 状态码200且URL已改变: {login_response.url}")
                    return True

            # 检查cookies变化 - 这个判断不太可靠，因为失败的请求也可能设置cookies
            # 只有当cookies数量明显增加时才认为可能成功
            initial_cookies_count = len(initial_cookies) if 'initial_cookies' in locals() else 0
            current_cookies_count = len(self.session.cookies.get_dict())
            if current_cookies_count > initial_cookies_count + 2:  # 至少增加了2个cookies
                logger.info(f"可能登录成功 - cookies数量从{initial_cookies_count}增加到{current_cookies_count}")
                return True

            logger.debug(f"无法确定登录状态，假定失败")
            return False

        except Exception as e:
            logger.error(f"登录尝试时出错: {e}")
            return False

    def _scan_worker(self, password):
        """扫描工作线程"""
        try:
            username = self.username if self.username else ""
            logger.debug(f"尝试密码: {password}")

            if self._try_login(username, password):
                with self.lock:
                    self.successful_passwords.append({
                        'username': username,
                        'password': password
                    })
                logger.info(f"发现有效密码: {username} / {password}")
                return True

            return False
        except Exception as e:
            logger.error(f"扫描工作线程出错: {e}")
            return False

    def start_scan(self):
        """开始扫描"""
        logger.info(f"开始扫描目标: {self.target_url}")
        logger.info(f"使用 {len(self.passwords)} 个密码进行测试")

        start_time = time.time()

        with ThreadPoolExecutor(max_workers=self.threads) as executor:
            # 执行扫描并忽略返回结果（我们通过self.successful_passwords获取结果）
            list(executor.map(self._scan_worker, self.passwords))

        end_time = time.time()
        duration = end_time - start_time

        logger.info(f"扫描完成，耗时: {duration:.2f} 秒")
        logger.info(f"测试了 {len(self.passwords)} 个密码")
        logger.info(f"发现 {len(self.successful_passwords)} 个有效密码")

        if self.successful_passwords:
            logger.info("有效密码列表:")
            for cred in self.successful_passwords:
                logger.info(f"用户名: {cred['username'] or '(空)'}, 密码: {cred['password']}")

        return self.successful_passwords

    def save_results(self, output_file):
        """保存结果到文件"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'target': self.target_url,
                    'scan_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'total_passwords_tested': len(self.passwords),
                    'successful_passwords': self.successful_passwords
                }, f, ensure_ascii=False, indent=4)
            logger.info(f"结果已保存到: {output_file}")
            return True
        except Exception as e:
            logger.error(f"保存结果时出错: {e}")
            return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='弱口令密码扫描工具')
    parser.add_argument('-u', '--url', required=True, help='目标URL')
    parser.add_argument('-n', '--username', help='用户名（如果已知）')
    parser.add_argument('-f', '--password-file', help='密码字典文件路径')
    parser.add_argument('-t', '--threads', type=int, default=5, help='线程数（默认: 5）')
    parser.add_argument('-o', '--output', default='scan_results.json', help='输出文件路径（默认: scan_results.json）')
    parser.add_argument('--timeout', type=int, default=10, help='请求超时时间（默认: 10秒）')
    parser.add_argument('-d', '--debug', action='store_true', help='启用调试模式')
    parser.add_argument('-v', '--verbose', action='store_true', help='显示详细信息')
    parser.add_argument('-s', '--sleep', type=float, default=0, help='每次请求之间的延迟时间（秒）（默认: 0）')
    parser.add_argument('-p', '--password', help='指定单个密码进行测试')
    parser.add_argument('--user-field', default='username', help='用户名字段名（默认: username）')
    parser.add_argument('--pass-field', default='password', help='密码字段名（默认: password）')

    args = parser.parse_args()

    # 如果启用了调试模式
    if args.debug:
        enable_debug_mode()

    # 如果启用了详细模式但没有启用调试模式
    elif args.verbose:
        logger.setLevel(logging.DEBUG)

    # 创建扫描器实例
    scanner = WeakPasswordScanner(
        target_url=args.url,
        username=args.username,
        password_file=args.password_file,
        threads=args.threads,
        timeout=args.timeout
    )

    # 添加请求之间的延迟
    if args.sleep > 0:
        logger.info(f"设置请求延迟: {args.sleep} 秒")
        # 使用猴子补丁修改_try_login方法，添加延迟
        original_try_login = scanner._try_login

        def try_login_with_delay(username, password):
            if args.sleep > 0:
                time.sleep(args.sleep)
            return original_try_login(username, password)

        scanner._try_login = try_login_with_delay

    # 如果指定了单个密码，只测试这个密码
    if args.password:
        logger.info(f"使用指定密码进行测试: {args.password}")
        username = args.username if args.username else ""
        if scanner._try_login(username, args.password):
            logger.info(f"发现有效密码: {username} / {args.password}")
            scanner.successful_passwords.append({
                'username': username,
                'password': args.password
            })
        else:
            logger.info(f"指定密码无效: {args.password}")
    else:
        # 执行完整扫描
        scanner.start_scan()

    # 始终保存结果，即使没有找到有效密码
    scanner.save_results(args.output)

    # 显示结果摘要
    if scanner.successful_passwords:
        logger.info("扫描结果: 发现以下有效密码")
        for cred in scanner.successful_passwords:
            logger.info(f"用户名: {cred['username'] or '(空)'}, 密码: {cred['password']}")
    else:
        logger.info("扫描结果: 未发现有效密码")

if __name__ == "__main__":
    main()
